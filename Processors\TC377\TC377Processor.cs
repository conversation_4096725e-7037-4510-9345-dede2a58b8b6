using System;
using System.Collections.Generic;
using System.Linq;
using CalculateStackSize.Models;
using CalculateStackSize.Processors.RH850; // 引用接口定义

namespace CalculateStackSize.Processors.TC377
{
    /// <summary>
    /// TC377 TriCore架构处理器特定实现
    /// </summary>
    public class TC377Processor : IProcessorSpecific
    {
        public ProcessorArchitecture Architecture => ProcessorArchitecture.TC377;
        public string ArchitectureName => "TC377 TriCore";
        public int PointerSize => 4; // 32位架构
        public int WordSize => 4;
        public int StackAlignment => 8; // 8字节对齐

        private readonly TC377CallingConvention _callingConvention;
        private readonly TC377RegisterInfo _registerInfo;

        public TC377Processor()
        {
            _callingConvention = new TC377CallingConvention();
            _registerInfo = new TC377RegisterInfo();
        }

        /// <summary>
        /// 计算函数的寄存器保存大小
        /// </summary>
        public int CalculateRegisterSaveSize(Function function)
        {
            // TC377 TriCore架构的寄存器保存规则
            int saveSize = 0;

            // 基本的callee-saved寄存器
            var calleeSavedRegisters = _registerInfo.GetCalleeSavedRegisters();
            
            // 根据函数特征确定需要保存的寄存器
            var registersToSave = DetermineRegistersToSave(function, calleeSavedRegisters);
            
            saveSize = registersToSave.Count * 4; // 每个寄存器4字节

            // 如果是中断处理函数，需要保存更多寄存器
            if (function.Type == FunctionType.InterruptHandler)
            {
                saveSize += CalculateInterruptContextSaveSize();
            }

            // TriCore特有的上下文保存
            if (function.CalledFunctions.Count > 0)
            {
                saveSize += CalculateTriCoreContextSaveSize();
            }

            return saveSize;
        }

        /// <summary>
        /// 计算参数传递的堆栈大小
        /// </summary>
        public int CalculateParameterStackSize(List<Parameter> parameters)
        {
            return _callingConvention.CalculateParameterStackSize(parameters);
        }

        /// <summary>
        /// 确定参数传递方式
        /// </summary>
        public ParameterPassingMethod DetermineParameterPassingMethod(Parameter parameter, int parameterIndex)
        {
            return _callingConvention.DetermineParameterPassingMethod(parameter, parameterIndex);
        }

        /// <summary>
        /// 计算堆栈帧对齐
        /// </summary>
        public int CalculateStackFrameAlignment(int currentSize)
        {
            int remainder = currentSize % StackAlignment;
            return remainder == 0 ? 0 : StackAlignment - remainder;
        }

        /// <summary>
        /// 获取函数调用开销
        /// </summary>
        public int GetFunctionCallOverhead()
        {
            // TC377函数调用的基本开销
            return 8; // 返回地址 + 上下文信息
        }

        /// <summary>
        /// 分析汇编指令的堆栈影响
        /// </summary>
        public int AnalyzeAssemblyStackImpact(string assemblyInstruction)
        {
            var instruction = assemblyInstruction.Trim().ToLower();
            
            // 分析TriCore堆栈操作指令
            if (instruction.StartsWith("st.w") || instruction.StartsWith("st.h") || instruction.StartsWith("st.b"))
            {
                return AnalyzeStoreInstruction(instruction);
            }
            else if (instruction.StartsWith("ld.w") || instruction.StartsWith("ld.h") || instruction.StartsWith("ld.b"))
            {
                return AnalyzeLoadInstruction(instruction);
            }
            else if (instruction.StartsWith("add.a") && instruction.Contains("sp"))
            {
                return AnalyzeStackPointerOperation(instruction);
            }
            else if (instruction.StartsWith("stm.a") || instruction.StartsWith("stm.d"))
            {
                return AnalyzeStoreMultipleInstruction(instruction);
            }
            else if (instruction.StartsWith("ldm.a") || instruction.StartsWith("ldm.d"))
            {
                return AnalyzeLoadMultipleInstruction(instruction);
            }
            else if (instruction.StartsWith("call") || instruction.StartsWith("calla"))
            {
                return AnalyzeCallInstruction(instruction);
            }

            return 0; // 不影响堆栈
        }

        /// <summary>
        /// 获取中断向量表信息
        /// </summary>
        public Dictionary<int, string> GetInterruptVectorTable()
        {
            return new Dictionary<int, string>
            {
                [0] = "Reset",
                [1] = "TLB_Fill",
                [2] = "TLB_Map",
                [3] = "TLB_Protection",
                [4] = "Privilege_Instruction",
                [5] = "Memory_Protection",
                [6] = "Instruction_Fetch",
                [7] = "Illegal_Opcode",
                [8] = "Unimplemented_Opcode",
                [9] = "Privilege_Violation",
                [10] = "Arithmetic_Overflow",
                // ... 更多TC377特定的中断向量
            };
        }

        /// <summary>
        /// 确定需要保存的寄存器
        /// </summary>
        private List<string> DetermineRegistersToSave(Function function, List<string> calleeSavedRegisters)
        {
            var registersToSave = new List<string>();

            // 基于函数复杂度确定需要保存的寄存器
            if (function.CalledFunctions.Count > 0)
            {
                // 如果函数调用其他函数，需要保存链接寄存器
                registersToSave.Add("A11"); // 返回地址寄存器
            }

            // TriCore有16个地址寄存器(A0-A15)和16个数据寄存器(D0-D15)
            if (function.LocalVariables.Count > 6)
            {
                // 如果局部变量较多，可能需要更多寄存器
                registersToSave.AddRange(calleeSavedRegisters.Take(6));
            }
            else if (function.LocalVariables.Count > 2)
            {
                registersToSave.AddRange(calleeSavedRegisters.Take(3));
            }

            return registersToSave.Distinct().ToList();
        }

        /// <summary>
        /// 计算中断上下文保存大小
        /// </summary>
        private int CalculateInterruptContextSaveSize()
        {
            // TC377中断处理需要保存的寄存器
            var interruptSaveRegisters = new[]
            {
                // 地址寄存器
                "A0", "A1", "A2", "A3", "A4", "A5", "A6", "A7",
                "A8", "A9", "A10", "A11", "A12", "A13", "A14", "A15",
                // 数据寄存器
                "D0", "D1", "D2", "D3", "D4", "D5", "D6", "D7",
                "D8", "D9", "D10", "D11", "D12", "D13", "D14", "D15",
                // 系统寄存器
                "PSW", "PC", "PCXI", "FCX"
            };

            return interruptSaveRegisters.Length * 4; // 每个寄存器4字节
        }

        /// <summary>
        /// 计算TriCore特有的上下文保存大小
        /// </summary>
        private int CalculateTriCoreContextSaveSize()
        {
            // TriCore的上下文切换单元(CSA)相关的开销
            return 16 * 4; // 16个寄存器的上下文
        }

        /// <summary>
        /// 分析存储指令
        /// </summary>
        private int AnalyzeStoreInstruction(string instruction)
        {
            if (instruction.Contains("sp") || instruction.Contains("a10"))
            {
                // A10是堆栈指针寄存器
                if (instruction.StartsWith("st.w"))
                    return -4; // 存储4字节到堆栈
                else if (instruction.StartsWith("st.h"))
                    return -2; // 存储2字节到堆栈
                else if (instruction.StartsWith("st.b"))
                    return -1; // 存储1字节到堆栈
            }
            return 0;
        }

        /// <summary>
        /// 分析加载指令
        /// </summary>
        private int AnalyzeLoadInstruction(string instruction)
        {
            if (instruction.Contains("sp") || instruction.Contains("a10"))
            {
                if (instruction.StartsWith("ld.w"))
                    return 4; // 从堆栈加载4字节
                else if (instruction.StartsWith("ld.h"))
                    return 2; // 从堆栈加载2字节
                else if (instruction.StartsWith("ld.b"))
                    return 1; // 从堆栈加载1字节
            }
            return 0;
        }

        /// <summary>
        /// 分析堆栈指针操作
        /// </summary>
        private int AnalyzeStackPointerOperation(string instruction)
        {
            // 解析add.a sp, immediate形式的指令
            var parts = instruction.Split(new[] { ' ', ',' }, StringSplitOptions.RemoveEmptyEntries);
            
            if (parts.Length >= 3 && (parts[1] == "sp" || parts[1] == "a10"))
            {
                if (int.TryParse(parts[2], out int immediate))
                {
                    return immediate; // 正值表示堆栈增长，负值表示堆栈收缩
                }
            }
            
            return 0;
        }

        /// <summary>
        /// 分析存储多个寄存器指令
        /// </summary>
        private int AnalyzeStoreMultipleInstruction(string instruction)
        {
            // STM.A和STM.D指令分析
            if (instruction.StartsWith("stm.a"))
            {
                // 存储多个地址寄存器
                return -16; // 假设存储4个地址寄存器
            }
            else if (instruction.StartsWith("stm.d"))
            {
                // 存储多个数据寄存器
                return -16; // 假设存储4个数据寄存器
            }
            
            return 0;
        }

        /// <summary>
        /// 分析加载多个寄存器指令
        /// </summary>
        private int AnalyzeLoadMultipleInstruction(string instruction)
        {
            // LDM.A和LDM.D指令分析
            if (instruction.StartsWith("ldm.a"))
            {
                // 加载多个地址寄存器
                return 16; // 假设加载4个地址寄存器
            }
            else if (instruction.StartsWith("ldm.d"))
            {
                // 加载多个数据寄存器
                return 16; // 假设加载4个数据寄存器
            }
            
            return 0;
        }

        /// <summary>
        /// 分析调用指令
        /// </summary>
        private int AnalyzeCallInstruction(string instruction)
        {
            // CALL和CALLA指令会自动保存上下文
            if (instruction.StartsWith("call"))
            {
                return -64; // TriCore的上下文保存开销
            }
            
            return 0;
        }
    }

    /// <summary>
    /// TC377调用约定
    /// </summary>
    public class TC377CallingConvention
    {
        // TriCore使用A4-A7作为参数传递的地址寄存器
        // D4-D7作为参数传递的数据寄存器
        private readonly string[] _addressParameterRegisters = { "A4", "A5", "A6", "A7" };
        private readonly string[] _dataParameterRegisters = { "D4", "D5", "D6", "D7" };

        public int CalculateParameterStackSize(List<Parameter> parameters)
        {
            int stackSize = 0;
            int addressRegisterCount = 0;
            int dataRegisterCount = 0;

            foreach (var parameter in parameters)
            {
                if (IsPointerType(parameter.Type))
                {
                    // 指针类型使用地址寄存器
                    if (addressRegisterCount < _addressParameterRegisters.Length)
                    {
                        addressRegisterCount++;
                    }
                    else
                    {
                        stackSize += AlignSize(parameter.Size, 4);
                    }
                }
                else
                {
                    // 数据类型使用数据寄存器
                    if (dataRegisterCount < _dataParameterRegisters.Length && parameter.Size <= 4)
                    {
                        dataRegisterCount++;
                    }
                    else
                    {
                        stackSize += AlignSize(parameter.Size, 4);
                    }
                }
            }

            return stackSize;
        }

        public ParameterPassingMethod DetermineParameterPassingMethod(Parameter parameter, int parameterIndex)
        {
            if (IsPointerType(parameter.Type))
            {
                return parameterIndex < _addressParameterRegisters.Length ? 
                    ParameterPassingMethod.Register : ParameterPassingMethod.Stack;
            }
            else
            {
                return (parameterIndex < _dataParameterRegisters.Length && parameter.Size <= 4) ? 
                    ParameterPassingMethod.Register : ParameterPassingMethod.Stack;
            }
        }

        private bool IsPointerType(string type)
        {
            return type.Contains("*") || type.Contains("ptr");
        }

        private int AlignSize(int size, int alignment)
        {
            int remainder = size % alignment;
            return remainder == 0 ? size : size + alignment - remainder;
        }
    }

    /// <summary>
    /// TC377寄存器信息
    /// </summary>
    public class TC377RegisterInfo
    {
        public List<string> GetCalleeSavedRegisters()
        {
            return new List<string>
            {
                // 地址寄存器 (callee-saved)
                "A12", "A13", "A14", "A15",
                // 数据寄存器 (callee-saved)
                "D12", "D13", "D14", "D15"
            };
        }

        public List<string> GetCallerSavedRegisters()
        {
            return new List<string>
            {
                // 地址寄存器 (caller-saved)
                "A2", "A3", "A4", "A5", "A6", "A7", "A8", "A9",
                // 数据寄存器 (caller-saved)
                "D0", "D1", "D2", "D3", "D4", "D5", "D6", "D7", "D8", "D9", "D10", "D11"
            };
        }

        public List<string> GetSpecialRegisters()
        {
            return new List<string>
            {
                "A0",   // 全局地址寄存器
                "A1",   // 全局地址寄存器
                "A10",  // 堆栈指针
                "A11",  // 返回地址寄存器
                "PSW",  // 程序状态字
                "PC",   // 程序计数器
                "PCXI", // 程序计数器扩展信息
                "FCX"   // 自由上下文列表头指针
            };
        }

        public List<string> GetAddressRegisters()
        {
            return Enumerable.Range(0, 16).Select(i => $"A{i}").ToList();
        }

        public List<string> GetDataRegisters()
        {
            return Enumerable.Range(0, 16).Select(i => $"D{i}").ToList();
        }
    }
}
