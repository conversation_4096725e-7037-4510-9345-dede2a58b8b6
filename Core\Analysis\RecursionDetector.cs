using System;
using System.Collections.Generic;
using System.Linq;
using CalculateStackSize.Models;

namespace CalculateStackSize.Core.Analysis
{
    /// <summary>
    /// 递归调用检测器
    /// </summary>
    public class RecursionDetector
    {
        /// <summary>
        /// 检测调用图中的所有递归调用
        /// </summary>
        public RecursionAnalysisResult DetectRecursion(CallGraph callGraph)
        {
            var result = new RecursionAnalysisResult();
            var visited = new HashSet<Function>();
            var recursionStack = new HashSet<Function>();
            var pathStack = new Stack<Function>();

            foreach (var function in callGraph.Functions.Values)
            {
                if (!visited.Contains(function))
                {
                    DetectRecursionDFS(function, visited, recursionStack, pathStack, result);
                }
            }

            // 分析递归组的特征
            AnalyzeRecursionGroups(result);

            return result;
        }

        /// <summary>
        /// 使用深度优先搜索检测递归
        /// </summary>
        private void DetectRecursionDFS(Function current, HashSet<Function> visited, 
            HashSet<Function> recursionStack, Stack<Function> pathStack, RecursionAnalysisResult result)
        {
            visited.Add(current);
            recursionStack.Add(current);
            pathStack.Push(current);

            foreach (var call in current.CalledFunctions)
            {
                var calledFunction = call.Function;

                if (recursionStack.Contains(calledFunction))
                {
                    // 发现递归调用
                    var recursionPath = ExtractRecursionPath(pathStack, calledFunction);
                    var recursionGroup = CreateRecursionGroup(recursionPath, call);
                    
                    // 检查是否已存在相同的递归组
                    if (!result.RecursionGroups.Any(g => IsSameRecursionGroup(g, recursionGroup)))
                    {
                        result.RecursionGroups.Add(recursionGroup);
                    }

                    // 标记递归函数
                    foreach (var func in recursionPath)
                    {
                        func.IsRecursive = true;
                        if (!result.RecursiveFunctions.Contains(func))
                        {
                            result.RecursiveFunctions.Add(func);
                        }
                    }
                }
                else if (!visited.Contains(calledFunction))
                {
                    DetectRecursionDFS(calledFunction, visited, recursionStack, pathStack, result);
                }
            }

            recursionStack.Remove(current);
            pathStack.Pop();
        }

        /// <summary>
        /// 提取递归路径
        /// </summary>
        private List<Function> ExtractRecursionPath(Stack<Function> pathStack, Function recursionTarget)
        {
            var path = pathStack.ToList();
            path.Reverse(); // 因为栈是反向的

            var recursionStartIndex = path.IndexOf(recursionTarget);
            if (recursionStartIndex >= 0)
            {
                return path.Skip(recursionStartIndex).ToList();
            }

            return new List<Function> { recursionTarget };
        }

        /// <summary>
        /// 创建递归组
        /// </summary>
        private RecursionGroup CreateRecursionGroup(List<Function> recursionPath, FunctionCall triggeringCall)
        {
            var group = new RecursionGroup
            {
                Functions = new List<Function>(recursionPath),
                RecursionType = recursionPath.Count == 1 ? RecursionType.Direct : RecursionType.Indirect,
                TriggeringCall = triggeringCall,
                EstimatedMaxDepth = EstimateMaxRecursionDepth(recursionPath)
            };

            return group;
        }

        /// <summary>
        /// 检查两个递归组是否相同
        /// </summary>
        private bool IsSameRecursionGroup(RecursionGroup group1, RecursionGroup group2)
        {
            if (group1.Functions.Count != group2.Functions.Count)
                return false;

            // 检查是否包含相同的函数集合（顺序可能不同）
            return group1.Functions.All(f => group2.Functions.Contains(f)) &&
                   group2.Functions.All(f => group1.Functions.Contains(f));
        }

        /// <summary>
        /// 估算最大递归深度
        /// </summary>
        private int EstimateMaxRecursionDepth(List<Function> recursionPath)
        {
            // 这是一个简化的估算方法
            // 实际应用中可能需要更复杂的分析，包括：
            // 1. 分析递归终止条件
            // 2. 分析输入参数的变化模式
            // 3. 考虑实际的业务逻辑

            if (recursionPath.Count == 1)
            {
                // 直接递归，通常深度较小
                return 10;
            }
            else
            {
                // 间接递归，可能深度更大
                return 20;
            }
        }

        /// <summary>
        /// 分析递归组的特征
        /// </summary>
        private void AnalyzeRecursionGroups(RecursionAnalysisResult result)
        {
            foreach (var group in result.RecursionGroups)
            {
                // 分析递归组的风险级别
                group.RiskLevel = AnalyzeRecursionRisk(group);

                // 分析递归模式
                group.RecursionPattern = AnalyzeRecursionPattern(group);

                // 生成建议
                group.Recommendations = GenerateRecursionRecommendations(group);
            }
        }

        /// <summary>
        /// 分析递归风险级别
        /// </summary>
        private RecursionRiskLevel AnalyzeRecursionRisk(RecursionGroup group)
        {
            int riskScore = 0;

            // 基于递归深度
            if (group.EstimatedMaxDepth > 50)
                riskScore += 3;
            else if (group.EstimatedMaxDepth > 20)
                riskScore += 2;
            else if (group.EstimatedMaxDepth > 10)
                riskScore += 1;

            // 基于递归组大小
            if (group.Functions.Count > 5)
                riskScore += 2;
            else if (group.Functions.Count > 2)
                riskScore += 1;

            // 基于函数类型
            if (group.Functions.Any(f => f.Type == FunctionType.InterruptHandler))
                riskScore += 3; // 中断处理函数中的递归风险很高

            if (group.Functions.Any(f => f.Type == FunctionType.TaskFunction))
                riskScore += 2; // 任务函数中的递归也有风险

            // 基于堆栈使用
            int totalStackUsage = group.Functions.Sum(f => f.GetBaseStackSize());
            if (totalStackUsage > 1000)
                riskScore += 2;
            else if (totalStackUsage > 500)
                riskScore += 1;

            // 确定风险级别
            if (riskScore >= 6)
                return RecursionRiskLevel.Critical;
            else if (riskScore >= 4)
                return RecursionRiskLevel.High;
            else if (riskScore >= 2)
                return RecursionRiskLevel.Medium;
            else
                return RecursionRiskLevel.Low;
        }

        /// <summary>
        /// 分析递归模式
        /// </summary>
        private RecursionPattern AnalyzeRecursionPattern(RecursionGroup group)
        {
            if (group.RecursionType == RecursionType.Direct)
            {
                // 直接递归模式分析
                var function = group.Functions.First();
                
                // 检查是否有明显的终止条件
                if (HasObviousTerminationCondition(function))
                    return RecursionPattern.TailRecursion;
                else
                    return RecursionPattern.LinearRecursion;
            }
            else
            {
                // 间接递归模式分析
                if (group.Functions.Count == 2)
                    return RecursionPattern.MutualRecursion;
                else
                    return RecursionPattern.ComplexRecursion;
            }
        }

        /// <summary>
        /// 检查函数是否有明显的终止条件
        /// </summary>
        private bool HasObviousTerminationCondition(Function function)
        {
            // 这里是一个简化的实现
            // 实际应该分析函数的源代码来检测终止条件
            
            // 如果函数有参数，可能有终止条件
            if (function.Parameters.Any(p => p.Type.Contains("int") || p.Type.Contains("count")))
                return true;

            // 如果函数名包含某些关键词，可能有终止条件
            var terminationKeywords = new[] { "count", "index", "depth", "level", "size" };
            if (terminationKeywords.Any(keyword => function.Name.ToLower().Contains(keyword)))
                return true;

            return false;
        }

        /// <summary>
        /// 生成递归相关的建议
        /// </summary>
        private List<string> GenerateRecursionRecommendations(RecursionGroup group)
        {
            var recommendations = new List<string>();

            switch (group.RiskLevel)
            {
                case RecursionRiskLevel.Critical:
                    recommendations.Add("严重警告：递归调用风险极高，建议重构为迭代实现");
                    recommendations.Add("如果必须使用递归，请严格限制递归深度");
                    recommendations.Add("考虑增加堆栈大小配置");
                    break;

                case RecursionRiskLevel.High:
                    recommendations.Add("高风险：建议添加递归深度限制");
                    recommendations.Add("考虑优化递归算法或改为迭代实现");
                    recommendations.Add("增加堆栈溢出检测");
                    break;

                case RecursionRiskLevel.Medium:
                    recommendations.Add("中等风险：建议添加递归深度监控");
                    recommendations.Add("确保有明确的递归终止条件");
                    break;

                case RecursionRiskLevel.Low:
                    recommendations.Add("低风险：确保递归深度在合理范围内");
                    break;
            }

            // 基于递归模式的建议
            switch (group.RecursionPattern)
            {
                case RecursionPattern.TailRecursion:
                    recommendations.Add("尾递归可以优化为循环实现");
                    break;

                case RecursionPattern.MutualRecursion:
                    recommendations.Add("相互递归需要特别注意调用深度");
                    break;

                case RecursionPattern.ComplexRecursion:
                    recommendations.Add("复杂递归建议重构为更简单的结构");
                    break;
            }

            return recommendations;
        }
    }

    /// <summary>
    /// 递归分析结果
    /// </summary>
    public class RecursionAnalysisResult
    {
        public List<RecursionGroup> RecursionGroups { get; set; }
        public List<Function> RecursiveFunctions { get; set; }
        public int TotalRecursionGroups => RecursionGroups.Count;
        public int TotalRecursiveFunctions => RecursiveFunctions.Count;

        public RecursionAnalysisResult()
        {
            RecursionGroups = new List<RecursionGroup>();
            RecursiveFunctions = new List<Function>();
        }
    }

    /// <summary>
    /// 递归风险级别
    /// </summary>
    public enum RecursionRiskLevel
    {
        Low,      // 低风险
        Medium,   // 中等风险
        High,     // 高风险
        Critical  // 严重风险
    }

    /// <summary>
    /// 递归模式
    /// </summary>
    public enum RecursionPattern
    {
        LinearRecursion,   // 线性递归
        TailRecursion,     // 尾递归
        MutualRecursion,   // 相互递归
        ComplexRecursion   // 复杂递归
    }
}

// 扩展RecursionGroup类
namespace CalculateStackSize.Models
{
    public partial class RecursionGroup
    {
        public FunctionCall TriggeringCall { get; set; }
        public RecursionRiskLevel RiskLevel { get; set; }
        public RecursionPattern RecursionPattern { get; set; }
        public List<string> Recommendations { get; set; }

        partial void InitializeExtensions()
        {
            Recommendations = new List<string>();
        }
    }

    public enum RecursionRiskLevel
    {
        Low,
        Medium,
        High,
        Critical
    }

    public enum RecursionPattern
    {
        LinearRecursion,
        TailRecursion,
        MutualRecursion,
        ComplexRecursion
    }
}
