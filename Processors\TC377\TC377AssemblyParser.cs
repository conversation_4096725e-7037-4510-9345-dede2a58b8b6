using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using CalculateStackSize.Models;
using CalculateStackSize.Processors.RH850; // 引用共同的类型定义

namespace CalculateStackSize.Processors.TC377
{
    /// <summary>
    /// TC377 TriCore汇编代码解析器
    /// </summary>
    public class TC377AssemblyParser
    {
        private readonly TC377Processor _processor;
        private readonly Dictionary<string, TC377Instruction> _instructionSet;

        public TC377AssemblyParser()
        {
            _processor = new TC377Processor();
            _instructionSet = InitializeInstructionSet();
        }

        /// <summary>
        /// 解析汇编文件
        /// </summary>
        public List<AssemblyFunction> ParseAssemblyFile(string filePath)
        {
            if (!File.Exists(filePath))
                throw new FileNotFoundException($"Assembly file not found: {filePath}");

            string content = File.ReadAllText(filePath);
            return ParseAssemblyContent(content, filePath);
        }

        /// <summary>
        /// 解析汇编内容
        /// </summary>
        public List<AssemblyFunction> ParseAssemblyContent(string content, string sourceFile)
        {
            var functions = new List<AssemblyFunction>();
            var lines = content.Split('\n');
            
            AssemblyFunction currentFunction = null;
            
            for (int i = 0; i < lines.Length; i++)
            {
                string line = lines[i].Trim();
                
                if (string.IsNullOrEmpty(line) || line.StartsWith(";") || line.StartsWith("#"))
                    continue; // 跳过空行和注释
                
                // 检查函数标签
                if (IsFunctionLabel(line))
                {
                    if (currentFunction != null)
                    {
                        functions.Add(currentFunction);
                    }
                    
                    currentFunction = new AssemblyFunction
                    {
                        Name = ExtractFunctionName(line),
                        SourceFile = sourceFile,
                        StartLine = i + 1,
                        Instructions = new List<AssemblyInstruction>()
                    };
                }
                else if (currentFunction != null && IsInstruction(line))
                {
                    var instruction = ParseInstruction(line, i + 1);
                    if (instruction != null)
                    {
                        currentFunction.Instructions.Add(instruction);
                        
                        // 分析堆栈影响
                        AnalyzeStackImpact(instruction, currentFunction);
                    }
                }
            }
            
            if (currentFunction != null)
            {
                functions.Add(currentFunction);
            }
            
            return functions;
        }

        /// <summary>
        /// 检查是否为函数标签
        /// </summary>
        private bool IsFunctionLabel(string line)
        {
            // TriCore汇编中函数标签通常以冒号结尾
            return Regex.IsMatch(line, @"^[a-zA-Z_][a-zA-Z0-9_]*:$");
        }

        /// <summary>
        /// 提取函数名
        /// </summary>
        private string ExtractFunctionName(string line)
        {
            return line.TrimEnd(':');
        }

        /// <summary>
        /// 检查是否为指令
        /// </summary>
        private bool IsInstruction(string line)
        {
            // 检查是否以已知的指令助记符开始
            var parts = line.Split(new[] { ' ', '\t' }, StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length == 0) return false;
            
            string mnemonic = parts[0].ToLower();
            return _instructionSet.ContainsKey(mnemonic);
        }

        /// <summary>
        /// 解析指令
        /// </summary>
        private AssemblyInstruction ParseInstruction(string line, int lineNumber)
        {
            var parts = line.Split(new[] { ' ', '\t' }, StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length == 0) return null;
            
            string mnemonic = parts[0].ToLower();
            string operands = parts.Length > 1 ? string.Join(" ", parts.Skip(1)) : "";
            
            if (!_instructionSet.ContainsKey(mnemonic))
                return null;
            
            var instructionInfo = _instructionSet[mnemonic];
            
            return new AssemblyInstruction
            {
                Mnemonic = mnemonic,
                Operands = operands,
                LineNumber = lineNumber,
                OriginalText = line,
                InstructionType = instructionInfo.Type,
                StackImpact = CalculateInstructionStackImpact(mnemonic, operands)
            };
        }

        /// <summary>
        /// 分析堆栈影响
        /// </summary>
        private void AnalyzeStackImpact(AssemblyInstruction instruction, AssemblyFunction function)
        {
            function.TotalStackImpact += instruction.StackImpact;
            
            // 跟踪最大堆栈使用
            if (instruction.StackImpact < 0) // 负值表示堆栈增长
            {
                function.MaxStackUsage = Math.Max(function.MaxStackUsage, -function.TotalStackImpact);
            }
            
            // 检查特殊指令
            switch (instruction.InstructionType)
            {
                case InstructionType.FunctionCall:
                    AnalyzeFunctionCall(instruction, function);
                    break;
                    
                case InstructionType.Return:
                    function.HasReturn = true;
                    break;
                    
                case InstructionType.StackOperation:
                    function.StackOperations.Add(instruction);
                    break;
            }
        }

        /// <summary>
        /// 分析函数调用
        /// </summary>
        private void AnalyzeFunctionCall(AssemblyInstruction instruction, AssemblyFunction function)
        {
            // 提取被调用的函数名
            string calledFunction = ExtractCalledFunctionName(instruction.Operands);
            if (!string.IsNullOrEmpty(calledFunction))
            {
                function.CalledFunctions.Add(calledFunction);
            }
        }

        /// <summary>
        /// 提取被调用的函数名
        /// </summary>
        private string ExtractCalledFunctionName(string operands)
        {
            // 简化的函数名提取
            var match = Regex.Match(operands, @"([a-zA-Z_][a-zA-Z0-9_]*)");
            return match.Success ? match.Groups[1].Value : null;
        }

        /// <summary>
        /// 计算指令的堆栈影响
        /// </summary>
        private int CalculateInstructionStackImpact(string mnemonic, string operands)
        {
            switch (mnemonic)
            {
                // 存储指令
                case "st.w":
                    return operands.Contains("a10") || operands.Contains("sp") ? -4 : 0;
                case "st.h":
                    return operands.Contains("a10") || operands.Contains("sp") ? -2 : 0;
                case "st.b":
                    return operands.Contains("a10") || operands.Contains("sp") ? -1 : 0;
                case "st.d":
                    return operands.Contains("a10") || operands.Contains("sp") ? -8 : 0;
                    
                // 加载指令
                case "ld.w":
                    return operands.Contains("a10") || operands.Contains("sp") ? 4 : 0;
                case "ld.h":
                    return operands.Contains("a10") || operands.Contains("sp") ? 2 : 0;
                case "ld.b":
                    return operands.Contains("a10") || operands.Contains("sp") ? 1 : 0;
                case "ld.d":
                    return operands.Contains("a10") || operands.Contains("sp") ? 8 : 0;
                    
                // 地址运算指令
                case "add.a":
                    return AnalyzeAddressAddInstruction(operands);
                case "sub.a":
                    return AnalyzeAddressSubInstruction(operands);
                    
                // 多寄存器存储/加载
                case "stm.a":
                case "stm.d":
                    return AnalyzeStoreMultipleInstruction(operands);
                case "ldm.a":
                case "ldm.d":
                    return AnalyzeLoadMultipleInstruction(operands);
                    
                // 函数调用指令
                case "call":
                case "calla":
                case "fcall":
                case "fcalla":
                    return AnalyzeCallInstruction(mnemonic, operands);
                    
                // 返回指令
                case "ret":
                case "rfe":
                    return AnalyzeReturnInstruction(mnemonic);
                    
                default:
                    return 0;
            }
        }

        /// <summary>
        /// 分析地址加法指令
        /// </summary>
        private int AnalyzeAddressAddInstruction(string operands)
        {
            // 解析 "add.a a10, immediate" 形式的指令
            var parts = operands.Split(',');
            if (parts.Length == 2 && (parts[0].Trim() == "a10" || parts[0].Trim() == "sp"))
            {
                if (int.TryParse(parts[1].Trim(), out int immediate))
                {
                    return immediate;
                }
            }
            return 0;
        }

        /// <summary>
        /// 分析地址减法指令
        /// </summary>
        private int AnalyzeAddressSubInstruction(string operands)
        {
            // 解析 "sub.a a10, immediate" 形式的指令
            var parts = operands.Split(',');
            if (parts.Length == 2 && (parts[0].Trim() == "a10" || parts[0].Trim() == "sp"))
            {
                if (int.TryParse(parts[1].Trim(), out int immediate))
                {
                    return -immediate; // 减法操作，取负值
                }
            }
            return 0;
        }

        /// <summary>
        /// 分析存储多个寄存器指令
        /// </summary>
        private int AnalyzeStoreMultipleInstruction(string operands)
        {
            // STM.A和STM.D指令分析
            // 简化处理，假设存储4个寄存器
            if (operands.Contains("a10") || operands.Contains("sp"))
            {
                return -16; // 4个寄存器 * 4字节
            }
            return 0;
        }

        /// <summary>
        /// 分析加载多个寄存器指令
        /// </summary>
        private int AnalyzeLoadMultipleInstruction(string operands)
        {
            // LDM.A和LDM.D指令分析
            // 简化处理，假设加载4个寄存器
            if (operands.Contains("a10") || operands.Contains("sp"))
            {
                return 16; // 4个寄存器 * 4字节
            }
            return 0;
        }

        /// <summary>
        /// 分析调用指令
        /// </summary>
        private int AnalyzeCallInstruction(string mnemonic, string operands)
        {
            // TriCore的调用指令会自动保存上下文
            switch (mnemonic)
            {
                case "call":
                case "calla":
                    return -64; // 标准调用的上下文保存开销
                case "fcall":
                case "fcalla":
                    return -128; // 远调用的上下文保存开销更大
                default:
                    return 0;
            }
        }

        /// <summary>
        /// 分析返回指令
        /// </summary>
        private int AnalyzeReturnInstruction(string mnemonic)
        {
            // 返回指令会恢复上下文
            switch (mnemonic)
            {
                case "ret":
                    return 64; // 标准返回的上下文恢复
                case "rfe":
                    return 128; // 异常返回的上下文恢复更大
                default:
                    return 0;
            }
        }

        /// <summary>
        /// 初始化指令集
        /// </summary>
        private Dictionary<string, TC377Instruction> InitializeInstructionSet()
        {
            return new Dictionary<string, TC377Instruction>
            {
                // 数据传输指令
                ["mov"] = new TC377Instruction { Type = InstructionType.DataTransfer },
                ["mov.a"] = new TC377Instruction { Type = InstructionType.DataTransfer },
                ["mov.d"] = new TC377Instruction { Type = InstructionType.DataTransfer },
                
                // 加载/存储指令
                ["ld.b"] = new TC377Instruction { Type = InstructionType.Load },
                ["ld.h"] = new TC377Instruction { Type = InstructionType.Load },
                ["ld.w"] = new TC377Instruction { Type = InstructionType.Load },
                ["ld.d"] = new TC377Instruction { Type = InstructionType.Load },
                ["ld.a"] = new TC377Instruction { Type = InstructionType.Load },
                ["st.b"] = new TC377Instruction { Type = InstructionType.Store },
                ["st.h"] = new TC377Instruction { Type = InstructionType.Store },
                ["st.w"] = new TC377Instruction { Type = InstructionType.Store },
                ["st.d"] = new TC377Instruction { Type = InstructionType.Store },
                ["st.a"] = new TC377Instruction { Type = InstructionType.Store },
                
                // 多寄存器加载/存储
                ["ldm.a"] = new TC377Instruction { Type = InstructionType.Load },
                ["ldm.d"] = new TC377Instruction { Type = InstructionType.Load },
                ["stm.a"] = new TC377Instruction { Type = InstructionType.Store },
                ["stm.d"] = new TC377Instruction { Type = InstructionType.Store },
                
                // 算术指令
                ["add"] = new TC377Instruction { Type = InstructionType.Arithmetic },
                ["add.a"] = new TC377Instruction { Type = InstructionType.Arithmetic },
                ["sub"] = new TC377Instruction { Type = InstructionType.Arithmetic },
                ["sub.a"] = new TC377Instruction { Type = InstructionType.Arithmetic },
                ["mul"] = new TC377Instruction { Type = InstructionType.Arithmetic },
                ["div"] = new TC377Instruction { Type = InstructionType.Arithmetic },
                
                // 逻辑指令
                ["and"] = new TC377Instruction { Type = InstructionType.Logic },
                ["or"] = new TC377Instruction { Type = InstructionType.Logic },
                ["xor"] = new TC377Instruction { Type = InstructionType.Logic },
                ["not"] = new TC377Instruction { Type = InstructionType.Logic },
                
                // 分支指令
                ["j"] = new TC377Instruction { Type = InstructionType.Branch },
                ["jz"] = new TC377Instruction { Type = InstructionType.Branch },
                ["jnz"] = new TC377Instruction { Type = InstructionType.Branch },
                ["jeq"] = new TC377Instruction { Type = InstructionType.Branch },
                ["jne"] = new TC377Instruction { Type = InstructionType.Branch },
                
                // 函数调用指令
                ["call"] = new TC377Instruction { Type = InstructionType.FunctionCall },
                ["calla"] = new TC377Instruction { Type = InstructionType.FunctionCall },
                ["fcall"] = new TC377Instruction { Type = InstructionType.FunctionCall },
                ["fcalla"] = new TC377Instruction { Type = InstructionType.FunctionCall },
                
                // 返回指令
                ["ret"] = new TC377Instruction { Type = InstructionType.Return },
                ["rfe"] = new TC377Instruction { Type = InstructionType.Return },
                
                // 系统指令
                ["nop"] = new TC377Instruction { Type = InstructionType.System },
                ["debug"] = new TC377Instruction { Type = InstructionType.System },
                ["disable"] = new TC377Instruction { Type = InstructionType.System },
                ["enable"] = new TC377Instruction { Type = InstructionType.System }
            };
        }
    }

    /// <summary>
    /// TC377指令信息
    /// </summary>
    public class TC377Instruction
    {
        public InstructionType Type { get; set; }
        public int CycleCount { get; set; }
        public string Description { get; set; }
        public bool AffectsFlags { get; set; }
    }
}
