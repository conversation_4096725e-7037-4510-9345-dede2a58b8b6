using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using CalculateStackSize.Models;

namespace CalculateStackSize.Reports
{
    /// <summary>
    /// 报告生成器
    /// </summary>
    public class ReportGenerator
    {
        /// <summary>
        /// 生成HTML格式的详细报告
        /// </summary>
        public void GenerateHtmlReport(AnalysisResult result, string outputPath)
        {
            var html = new StringBuilder();
            
            // HTML头部
            html.AppendLine("<!DOCTYPE html>");
            html.AppendLine("<html lang=\"en\">");
            html.AppendLine("<head>");
            html.AppendLine("    <meta charset=\"UTF-8\">");
            html.AppendLine("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">");
            html.AppendLine("    <title>AUTOSAR Stack Size Analysis Report</title>");
            html.AppendLine("    <style>");
            html.AppendLine(GetHtmlStyles());
            html.AppendLine("    </style>");
            html.AppendLine("</head>");
            html.AppendLine("<body>");
            
            // 报告内容
            html.AppendLine("    <div class=\"container\">");
            html.AppendLine("        <header>");
            html.AppendLine("            <h1>AUTOSAR Stack Size Analysis Report</h1>");
            html.AppendLine($"            <p>Generated on: {DateTime.Now:yyyy-MM-dd HH:mm:ss}</p>");
            html.AppendLine("        </header>");
            
            // 概览部分
            html.AppendLine(GenerateOverviewSection(result));
            
            // 统计信息部分
            html.AppendLine(GenerateStatisticsSection(result));
            
            // 任务分析部分
            html.AppendLine(GenerateTaskAnalysisSection(result));
            
            // 中断分析部分
            html.AppendLine(GenerateInterruptAnalysisSection(result));
            
            // 警告部分
            html.AppendLine(GenerateWarningsSection(result));
            
            // 调用图部分
            html.AppendLine(GenerateCallGraphSection(result));
            
            html.AppendLine("    </div>");
            html.AppendLine("</body>");
            html.AppendLine("</html>");
            
            File.WriteAllText(outputPath, html.ToString());
        }

        /// <summary>
        /// 生成CSV格式的数据报告
        /// </summary>
        public void GenerateCsvReport(AnalysisResult result, string outputPath)
        {
            var csv = new StringBuilder();
            
            // 任务数据
            csv.AppendLine("Task Analysis");
            csv.AppendLine("Task Name,Max Stack Usage (bytes),Configured Size (bytes),Utilization (%),Status");
            
            foreach (var task in result.TaskAnalyses)
            {
                string status = task.HasOverflowRisk ? "OVERFLOW RISK" : 
                               task.StackUtilization > 0.8 ? "HIGH USAGE" : "OK";
                csv.AppendLine($"{task.TaskName},{task.MaxStackUsage},{task.ConfiguredStackSize},{task.StackUtilization:P1},{status}");
            }
            
            csv.AppendLine();
            
            // 中断数据
            csv.AppendLine("Interrupt Analysis");
            csv.AppendLine("Interrupt Name,Priority,Max Stack Usage (bytes),Context Save (bytes),Handler Function");
            
            foreach (var interrupt in result.InterruptAnalyses)
            {
                csv.AppendLine($"{interrupt.InterruptName},{interrupt.Priority},{interrupt.MaxStackUsage},{interrupt.ContextSaveSize},{interrupt.HandlerFunction?.Name}");
            }
            
            csv.AppendLine();
            
            // 警告数据
            csv.AppendLine("Warnings");
            csv.AppendLine("Level,Type,Message,Function,Suggestion");
            
            foreach (var warning in result.Warnings)
            {
                csv.AppendLine($"{warning.Level},{warning.Type},\"{warning.Message}\",{warning.RelatedFunction?.Name},\"{warning.Suggestion}\"");
            }
            
            File.WriteAllText(outputPath, csv.ToString());
        }

        /// <summary>
        /// 生成JSON格式的数据报告
        /// </summary>
        public void GenerateJsonReport(AnalysisResult result, string outputPath)
        {
            var json = new StringBuilder();
            json.AppendLine("{");
            json.AppendLine($"  \"reportInfo\": {{");
            json.AppendLine($"    \"projectPath\": \"{EscapeJson(result.ProjectPath)}\",");
            json.AppendLine($"    \"architecture\": \"{result.Architecture}\",");
            json.AppendLine($"    \"analysisTime\": \"{result.AnalysisTime:yyyy-MM-ddTHH:mm:ss}\",");
            json.AppendLine($"    \"maxStackUsage\": {result.GetMaxStackUsage()},");
            json.AppendLine($"    \"totalStackRequirement\": {result.GetTotalStackRequirement()}");
            json.AppendLine($"  }},");
            
            // 统计信息
            if (result.Statistics != null)
            {
                json.AppendLine($"  \"statistics\": {{");
                json.AppendLine($"    \"functionCount\": {result.Statistics.FunctionCount},");
                json.AppendLine($"    \"taskCount\": {result.Statistics.TaskCount},");
                json.AppendLine($"    \"interruptCount\": {result.Statistics.InterruptCount},");
                json.AppendLine($"    \"recursiveFunctionCount\": {result.Statistics.RecursiveFunctionCount},");
                json.AppendLine($"    \"maxCallDepth\": {result.Statistics.MaxCallDepth},");
                json.AppendLine($"    \"averageStackSize\": {result.Statistics.AverageStackSize:F1},");
                json.AppendLine($"    \"analysisTimeMs\": {result.Statistics.AnalysisTimeMs}");
                json.AppendLine($"  }},");
            }
            
            // 任务分析
            json.AppendLine($"  \"tasks\": [");
            for (int i = 0; i < result.TaskAnalyses.Count; i++)
            {
                var task = result.TaskAnalyses[i];
                json.AppendLine($"    {{");
                json.AppendLine($"      \"name\": \"{EscapeJson(task.TaskName)}\",");
                json.AppendLine($"      \"maxStackUsage\": {task.MaxStackUsage},");
                json.AppendLine($"      \"configuredStackSize\": {task.ConfiguredStackSize},");
                json.AppendLine($"      \"utilization\": {task.StackUtilization:F3},");
                json.AppendLine($"      \"hasOverflowRisk\": {task.HasOverflowRisk.ToString().ToLower()}");
                json.Append($"    }}");
                if (i < result.TaskAnalyses.Count - 1) json.AppendLine(",");
                else json.AppendLine();
            }
            json.AppendLine($"  ],");
            
            // 中断分析
            json.AppendLine($"  \"interrupts\": [");
            for (int i = 0; i < result.InterruptAnalyses.Count; i++)
            {
                var interrupt = result.InterruptAnalyses[i];
                json.AppendLine($"    {{");
                json.AppendLine($"      \"name\": \"{EscapeJson(interrupt.InterruptName)}\",");
                json.AppendLine($"      \"priority\": {interrupt.Priority},");
                json.AppendLine($"      \"maxStackUsage\": {interrupt.MaxStackUsage},");
                json.AppendLine($"      \"contextSaveSize\": {interrupt.ContextSaveSize},");
                json.AppendLine($"      \"handlerFunction\": \"{EscapeJson(interrupt.HandlerFunction?.Name)}\"");
                json.Append($"    }}");
                if (i < result.InterruptAnalyses.Count - 1) json.AppendLine(",");
                else json.AppendLine();
            }
            json.AppendLine($"  ],");
            
            // 警告
            json.AppendLine($"  \"warnings\": [");
            for (int i = 0; i < result.Warnings.Count; i++)
            {
                var warning = result.Warnings[i];
                json.AppendLine($"    {{");
                json.AppendLine($"      \"level\": \"{warning.Level}\",");
                json.AppendLine($"      \"type\": \"{warning.Type}\",");
                json.AppendLine($"      \"message\": \"{EscapeJson(warning.Message)}\",");
                json.AppendLine($"      \"function\": \"{EscapeJson(warning.RelatedFunction?.Name)}\",");
                json.AppendLine($"      \"suggestion\": \"{EscapeJson(warning.Suggestion)}\"");
                json.Append($"    }}");
                if (i < result.Warnings.Count - 1) json.AppendLine(",");
                else json.AppendLine();
            }
            json.AppendLine($"  ]");
            
            json.AppendLine("}");
            
            File.WriteAllText(outputPath, json.ToString());
        }

        /// <summary>
        /// 生成文本格式的简要报告
        /// </summary>
        public void GenerateTextReport(AnalysisResult result, string outputPath)
        {
            var text = new StringBuilder();
            
            text.AppendLine("=".PadRight(80, '='));
            text.AppendLine("AUTOSAR STACK SIZE ANALYSIS REPORT");
            text.AppendLine("=".PadRight(80, '='));
            text.AppendLine();
            
            text.AppendLine($"Project Path: {result.ProjectPath}");
            text.AppendLine($"Architecture: {result.Architecture}");
            text.AppendLine($"Analysis Time: {result.AnalysisTime:yyyy-MM-dd HH:mm:ss}");
            text.AppendLine();
            
            // 概览
            text.AppendLine("OVERVIEW");
            text.AppendLine("-".PadRight(40, '-'));
            text.AppendLine($"Maximum Stack Usage: {result.GetMaxStackUsage()} bytes");
            text.AppendLine($"Total Stack Requirement: {result.GetTotalStackRequirement()} bytes");
            text.AppendLine();
            
            // 统计信息
            if (result.Statistics != null)
            {
                text.AppendLine("STATISTICS");
                text.AppendLine("-".PadRight(40, '-'));
                text.AppendLine($"Total Functions: {result.Statistics.FunctionCount}");
                text.AppendLine($"Tasks: {result.Statistics.TaskCount}");
                text.AppendLine($"Interrupts: {result.Statistics.InterruptCount}");
                text.AppendLine($"Recursive Functions: {result.Statistics.RecursiveFunctionCount}");
                text.AppendLine($"Max Call Depth: {result.Statistics.MaxCallDepth}");
                text.AppendLine($"Average Stack Size: {result.Statistics.AverageStackSize:F1} bytes");
                text.AppendLine($"Analysis Time: {result.Statistics.AnalysisTimeMs} ms");
                text.AppendLine();
            }
            
            // 任务分析
            if (result.TaskAnalyses.Any())
            {
                text.AppendLine("TASK ANALYSIS");
                text.AppendLine("-".PadRight(40, '-'));
                text.AppendLine($"{"Task Name",-20} {"Max Stack",-12} {"Config Size",-12} {"Usage",-8} {"Status",-15}");
                text.AppendLine("-".PadRight(67, '-'));
                
                foreach (var task in result.TaskAnalyses)
                {
                    string status = task.HasOverflowRisk ? "OVERFLOW RISK" : 
                                   task.StackUtilization > 0.8 ? "HIGH USAGE" : "OK";
                    text.AppendLine($"{task.TaskName,-20} {task.MaxStackUsage + " bytes",-12} {task.ConfiguredStackSize + " bytes",-12} {task.StackUtilization:P1,-8} {status,-15}");
                }
                text.AppendLine();
            }
            
            // 中断分析
            if (result.InterruptAnalyses.Any())
            {
                text.AppendLine("INTERRUPT ANALYSIS");
                text.AppendLine("-".PadRight(40, '-'));
                text.AppendLine($"{"Interrupt Name",-20} {"Priority",-8} {"Max Stack",-12} {"Context",-10} {"Handler",-20}");
                text.AppendLine("-".PadRight(70, '-'));
                
                foreach (var interrupt in result.InterruptAnalyses)
                {
                    text.AppendLine($"{interrupt.InterruptName,-20} {interrupt.Priority,-8} {interrupt.MaxStackUsage + " bytes",-12} {interrupt.ContextSaveSize + " bytes",-10} {interrupt.HandlerFunction?.Name,-20}");
                }
                text.AppendLine();
            }
            
            // 警告
            if (result.Warnings.Any())
            {
                text.AppendLine("WARNINGS");
                text.AppendLine("-".PadRight(40, '-'));
                
                var warningGroups = result.Warnings.GroupBy(w => w.Level);
                foreach (var group in warningGroups.OrderByDescending(g => g.Key))
                {
                    text.AppendLine($"{group.Key}: {group.Count()} warnings");
                    foreach (var warning in group.Take(5)) // 显示前5个警告
                    {
                        text.AppendLine($"  - {warning.Message}");
                        if (!string.IsNullOrEmpty(warning.Suggestion))
                        {
                            text.AppendLine($"    Suggestion: {warning.Suggestion}");
                        }
                    }
                    if (group.Count() > 5)
                    {
                        text.AppendLine($"  ... and {group.Count() - 5} more {group.Key} warnings");
                    }
                    text.AppendLine();
                }
            }
            
            File.WriteAllText(outputPath, text.ToString());
        }

        // 辅助方法
        private string GetHtmlStyles()
        {
            return @"
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        header { text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #007acc; }
        h1 { color: #007acc; margin: 0; }
        h2 { color: #333; border-bottom: 1px solid #ddd; padding-bottom: 10px; }
        .section { margin-bottom: 30px; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { padding: 8px 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; font-weight: bold; }
        .overflow-risk { background-color: #ffebee; }
        .high-usage { background-color: #fff3e0; }
        .warning-critical { background-color: #ffebee; }
        .warning-error { background-color: #fff3e0; }
        .warning-warning { background-color: #fffde7; }
        .warning-info { background-color: #e3f2fd; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px; }
        .stat-card { background-color: #f8f9fa; padding: 15px; border-radius: 4px; text-align: center; }
        .stat-value { font-size: 24px; font-weight: bold; color: #007acc; }
        .stat-label { font-size: 14px; color: #666; margin-top: 5px; }";
        }

        private string GenerateOverviewSection(AnalysisResult result)
        {
            var section = new StringBuilder();
            section.AppendLine("        <div class=\"section\">");
            section.AppendLine("            <h2>Overview</h2>");
            section.AppendLine("            <div class=\"stats-grid\">");
            section.AppendLine($"                <div class=\"stat-card\"><div class=\"stat-value\">{result.GetMaxStackUsage()}</div><div class=\"stat-label\">Max Stack Usage (bytes)</div></div>");
            section.AppendLine($"                <div class=\"stat-card\"><div class=\"stat-value\">{result.GetTotalStackRequirement()}</div><div class=\"stat-label\">Total Requirement (bytes)</div></div>");
            section.AppendLine($"                <div class=\"stat-card\"><div class=\"stat-value\">{result.TaskAnalyses.Count}</div><div class=\"stat-label\">Tasks</div></div>");
            section.AppendLine($"                <div class=\"stat-card\"><div class=\"stat-value\">{result.InterruptAnalyses.Count}</div><div class=\"stat-label\">Interrupts</div></div>");
            section.AppendLine("            </div>");
            section.AppendLine("        </div>");
            return section.ToString();
        }

        private string GenerateStatisticsSection(AnalysisResult result)
        {
            if (result.Statistics == null) return "";
            
            var section = new StringBuilder();
            section.AppendLine("        <div class=\"section\">");
            section.AppendLine("            <h2>Statistics</h2>");
            section.AppendLine("            <div class=\"stats-grid\">");
            section.AppendLine($"                <div class=\"stat-card\"><div class=\"stat-value\">{result.Statistics.FunctionCount}</div><div class=\"stat-label\">Total Functions</div></div>");
            section.AppendLine($"                <div class=\"stat-card\"><div class=\"stat-value\">{result.Statistics.RecursiveFunctionCount}</div><div class=\"stat-label\">Recursive Functions</div></div>");
            section.AppendLine($"                <div class=\"stat-card\"><div class=\"stat-value\">{result.Statistics.MaxCallDepth}</div><div class=\"stat-label\">Max Call Depth</div></div>");
            section.AppendLine($"                <div class=\"stat-card\"><div class=\"stat-value\">{result.Statistics.AverageStackSize:F1}</div><div class=\"stat-label\">Avg Stack Size (bytes)</div></div>");
            section.AppendLine($"                <div class=\"stat-card\"><div class=\"stat-value\">{result.Statistics.AnalysisTimeMs}</div><div class=\"stat-label\">Analysis Time (ms)</div></div>");
            section.AppendLine("            </div>");
            section.AppendLine("        </div>");
            return section.ToString();
        }

        private string GenerateTaskAnalysisSection(AnalysisResult result)
        {
            if (!result.TaskAnalyses.Any()) return "";
            
            var section = new StringBuilder();
            section.AppendLine("        <div class=\"section\">");
            section.AppendLine("            <h2>Task Analysis</h2>");
            section.AppendLine("            <table>");
            section.AppendLine("                <thead>");
            section.AppendLine("                    <tr><th>Task Name</th><th>Max Stack Usage</th><th>Configured Size</th><th>Utilization</th><th>Status</th></tr>");
            section.AppendLine("                </thead>");
            section.AppendLine("                <tbody>");
            
            foreach (var task in result.TaskAnalyses)
            {
                string rowClass = task.HasOverflowRisk ? "overflow-risk" : 
                                 task.StackUtilization > 0.8 ? "high-usage" : "";
                string status = task.HasOverflowRisk ? "OVERFLOW RISK" : 
                               task.StackUtilization > 0.8 ? "HIGH USAGE" : "OK";
                
                section.AppendLine($"                    <tr class=\"{rowClass}\">");
                section.AppendLine($"                        <td>{EscapeHtml(task.TaskName)}</td>");
                section.AppendLine($"                        <td>{task.MaxStackUsage} bytes</td>");
                section.AppendLine($"                        <td>{task.ConfiguredStackSize} bytes</td>");
                section.AppendLine($"                        <td>{task.StackUtilization:P1}</td>");
                section.AppendLine($"                        <td>{status}</td>");
                section.AppendLine("                    </tr>");
            }
            
            section.AppendLine("                </tbody>");
            section.AppendLine("            </table>");
            section.AppendLine("        </div>");
            return section.ToString();
        }

        private string GenerateInterruptAnalysisSection(AnalysisResult result)
        {
            if (!result.InterruptAnalyses.Any()) return "";
            
            var section = new StringBuilder();
            section.AppendLine("        <div class=\"section\">");
            section.AppendLine("            <h2>Interrupt Analysis</h2>");
            section.AppendLine("            <table>");
            section.AppendLine("                <thead>");
            section.AppendLine("                    <tr><th>Interrupt Name</th><th>Priority</th><th>Max Stack Usage</th><th>Context Save</th><th>Handler Function</th></tr>");
            section.AppendLine("                </thead>");
            section.AppendLine("                <tbody>");
            
            foreach (var interrupt in result.InterruptAnalyses)
            {
                section.AppendLine("                    <tr>");
                section.AppendLine($"                        <td>{EscapeHtml(interrupt.InterruptName)}</td>");
                section.AppendLine($"                        <td>{interrupt.Priority}</td>");
                section.AppendLine($"                        <td>{interrupt.MaxStackUsage} bytes</td>");
                section.AppendLine($"                        <td>{interrupt.ContextSaveSize} bytes</td>");
                section.AppendLine($"                        <td>{EscapeHtml(interrupt.HandlerFunction?.Name)}</td>");
                section.AppendLine("                    </tr>");
            }
            
            section.AppendLine("                </tbody>");
            section.AppendLine("            </table>");
            section.AppendLine("        </div>");
            return section.ToString();
        }

        private string GenerateWarningsSection(AnalysisResult result)
        {
            if (!result.Warnings.Any()) return "";
            
            var section = new StringBuilder();
            section.AppendLine("        <div class=\"section\">");
            section.AppendLine("            <h2>Warnings</h2>");
            section.AppendLine("            <table>");
            section.AppendLine("                <thead>");
            section.AppendLine("                    <tr><th>Level</th><th>Type</th><th>Message</th><th>Function</th><th>Suggestion</th></tr>");
            section.AppendLine("                </thead>");
            section.AppendLine("                <tbody>");
            
            foreach (var warning in result.Warnings)
            {
                string rowClass = $"warning-{warning.Level.ToString().ToLower()}";
                
                section.AppendLine($"                    <tr class=\"{rowClass}\">");
                section.AppendLine($"                        <td>{warning.Level}</td>");
                section.AppendLine($"                        <td>{warning.Type}</td>");
                section.AppendLine($"                        <td>{EscapeHtml(warning.Message)}</td>");
                section.AppendLine($"                        <td>{EscapeHtml(warning.RelatedFunction?.Name)}</td>");
                section.AppendLine($"                        <td>{EscapeHtml(warning.Suggestion)}</td>");
                section.AppendLine("                    </tr>");
            }
            
            section.AppendLine("                </tbody>");
            section.AppendLine("            </table>");
            section.AppendLine("        </div>");
            return section.ToString();
        }

        private string GenerateCallGraphSection(AnalysisResult result)
        {
            var section = new StringBuilder();
            section.AppendLine("        <div class=\"section\">");
            section.AppendLine("            <h2>Call Graph Summary</h2>");
            section.AppendLine($"            <p>Total Functions: {result.CallGraph.Functions.Count}</p>");
            section.AppendLine($"            <p>Entry Points: {result.CallGraph.EntryPoints.Count}</p>");
            section.AppendLine($"            <p>Recursion Groups: {result.CallGraph.RecursionGroups.Count}</p>");
            section.AppendLine($"            <p>Orphan Functions: {result.CallGraph.OrphanFunctions.Count}</p>");
            section.AppendLine("        </div>");
            return section.ToString();
        }

        private string EscapeHtml(string text)
        {
            if (string.IsNullOrEmpty(text)) return "";
            return text.Replace("&", "&amp;").Replace("<", "&lt;").Replace(">", "&gt;").Replace("\"", "&quot;");
        }

        private string EscapeJson(string text)
        {
            if (string.IsNullOrEmpty(text)) return "";
            return text.Replace("\\", "\\\\").Replace("\"", "\\\"").Replace("\n", "\\n").Replace("\r", "\\r").Replace("\t", "\\t");
        }

        /// <summary>
        /// 生成DOT格式的调用图文件（可用Graphviz渲染）
        /// </summary>
        public void GenerateCallGraphDot(AnalysisResult result, string outputPath)
        {
            var dot = new StringBuilder();

            dot.AppendLine("digraph CallGraph {");
            dot.AppendLine("    rankdir=TB;");
            dot.AppendLine("    node [shape=box, style=filled];");
            dot.AppendLine("    edge [color=gray];");
            dot.AppendLine();

            // 定义节点样式
            dot.AppendLine("    // Node styles");
            foreach (var function in result.CallGraph.Functions.Values)
            {
                string color = GetNodeColor(function);
                string label = $"{function.Name}\\n{function.GetBaseStackSize()} bytes";
                dot.AppendLine($"    \"{function.Name}\" [label=\"{label}\", fillcolor=\"{color}\"];");
            }

            dot.AppendLine();
            dot.AppendLine("    // Edges");

            // 添加调用关系
            foreach (var function in result.CallGraph.Functions.Values)
            {
                foreach (var call in function.CalledFunctions)
                {
                    string edgeStyle = call.CallType == CallType.Recursive ? "color=red, style=bold" : "color=gray";
                    dot.AppendLine($"    \"{function.Name}\" -> \"{call.Function.Name}\" [{edgeStyle}];");
                }
            }

            dot.AppendLine("}");

            File.WriteAllText(outputPath, dot.ToString());
        }

        /// <summary>
        /// 生成Mermaid格式的调用图
        /// </summary>
        public void GenerateCallGraphMermaid(AnalysisResult result, string outputPath)
        {
            var mermaid = new StringBuilder();

            mermaid.AppendLine("graph TD");
            mermaid.AppendLine("    %% AUTOSAR Stack Analysis Call Graph");
            mermaid.AppendLine();

            // 添加节点定义
            foreach (var function in result.CallGraph.Functions.Values.Take(20)) // 限制显示数量
            {
                string nodeId = GetMermaidNodeId(function.Name);
                string nodeClass = GetMermaidNodeClass(function);
                mermaid.AppendLine($"    {nodeId}[\"{function.Name}<br/>{function.GetBaseStackSize()} bytes\"]");
                if (!string.IsNullOrEmpty(nodeClass))
                {
                    mermaid.AppendLine($"    class {nodeId} {nodeClass}");
                }
            }

            mermaid.AppendLine();

            // 添加连接
            foreach (var function in result.CallGraph.Functions.Values.Take(20))
            {
                string fromId = GetMermaidNodeId(function.Name);
                foreach (var call in function.CalledFunctions.Take(5)) // 限制连接数量
                {
                    if (result.CallGraph.Functions.Values.Take(20).Contains(call.Function))
                    {
                        string toId = GetMermaidNodeId(call.Function.Name);
                        string linkStyle = call.CallType == CallType.Recursive ? "-.->|recursive|" : "-->";
                        mermaid.AppendLine($"    {fromId} {linkStyle} {toId}");
                    }
                }
            }

            mermaid.AppendLine();
            mermaid.AppendLine("    %% Node classes");
            mermaid.AppendLine("    classDef taskFunction fill:#e1f5fe");
            mermaid.AppendLine("    classDef interruptHandler fill:#fff3e0");
            mermaid.AppendLine("    classDef recursiveFunction fill:#ffebee");
            mermaid.AppendLine("    classDef normalFunction fill:#f5f5f5");

            File.WriteAllText(outputPath, mermaid.ToString());
        }

        private string GetNodeColor(Function function)
        {
            switch (function.Type)
            {
                case FunctionType.TaskFunction:
                    return "lightblue";
                case FunctionType.InterruptHandler:
                    return "orange";
                case FunctionType.HookFunction:
                    return "yellow";
                default:
                    return function.IsRecursive ? "lightcoral" : "lightgray";
            }
        }

        private string GetMermaidNodeId(string functionName)
        {
            // 清理函数名以适合Mermaid语法
            return functionName.Replace("_", "").Replace("-", "").Replace(".", "");
        }

        private string GetMermaidNodeClass(Function function)
        {
            if (function.IsRecursive)
                return "recursiveFunction";

            switch (function.Type)
            {
                case FunctionType.TaskFunction:
                    return "taskFunction";
                case FunctionType.InterruptHandler:
                    return "interruptHandler";
                default:
                    return "normalFunction";
            }
        }
    }
}
