using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Xml.Linq;
using CalculateStackSize.Models;

namespace CalculateStackSize.Core.Parser
{
    /// <summary>
    /// AUTOSAR配置文件解析器
    /// </summary>
    public class AutosarConfigParser
    {
        /// <summary>
        /// 解析AUTOSAR OS配置文件
        /// </summary>
        public AutosarConfiguration ParseConfiguration(string configFilePath)
        {
            if (!File.Exists(configFilePath))
                throw new FileNotFoundException($"AUTOSAR configuration file not found: {configFilePath}");

            var config = new AutosarConfiguration();
            
            try
            {
                // 根据文件扩展名选择解析方法
                string extension = Path.GetExtension(configFilePath).ToLower();
                
                switch (extension)
                {
                    case ".xml":
                    case ".arxml":
                        ParseXmlConfiguration(configFilePath, config);
                        break;
                    case ".oil":
                        ParseOilConfiguration(configFilePath, config);
                        break;
                    case ".c":
                    case ".h":
                        ParseCConfiguration(configFilePath, config);
                        break;
                    default:
                        throw new NotSupportedException($"Unsupported configuration file format: {extension}");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error parsing AUTOSAR configuration: {ex.Message}", ex);
            }
            
            return config;
        }

        /// <summary>
        /// 解析XML格式的AUTOSAR配置
        /// </summary>
        private void ParseXmlConfiguration(string filePath, AutosarConfiguration config)
        {
            var doc = XDocument.Load(filePath);
            
            // 解析任务配置
            ParseTasksFromXml(doc, config);
            
            // 解析中断配置
            ParseInterruptsFromXml(doc, config);
            
            // 解析堆栈配置
            ParseStackConfigFromXml(doc, config);
        }

        /// <summary>
        /// 解析OIL格式的AUTOSAR配置
        /// </summary>
        private void ParseOilConfiguration(string filePath, AutosarConfiguration config)
        {
            string content = File.ReadAllText(filePath);
            
            // 解析任务
            ParseTasksFromOil(content, config);
            
            // 解析中断
            ParseInterruptsFromOil(content, config);
            
            // 解析堆栈配置
            ParseStackConfigFromOil(content, config);
        }

        /// <summary>
        /// 解析C头文件中的配置
        /// </summary>
        private void ParseCConfiguration(string filePath, AutosarConfiguration config)
        {
            string content = File.ReadAllText(filePath);
            
            // 解析宏定义中的配置信息
            ParseTasksFromC(content, config);
            ParseInterruptsFromC(content, config);
            ParseStackConfigFromC(content, config);
        }

        /// <summary>
        /// 从XML解析任务配置
        /// </summary>
        private void ParseTasksFromXml(XDocument doc, AutosarConfiguration config)
        {
            var taskElements = doc.Descendants()
                .Where(e => e.Name.LocalName == "Task" || e.Name.LocalName == "OsTask");
            
            foreach (var taskElement in taskElements)
            {
                var task = new AutosarTask
                {
                    Name = GetAttributeValue(taskElement, "name") ?? GetElementValue(taskElement, "Name"),
                    Id = GetAttributeValue(taskElement, "id") ?? GetElementValue(taskElement, "TaskId"),
                    Priority = ParseInt(GetElementValue(taskElement, "Priority")) ?? 0,
                    StackSize = ParseInt(GetElementValue(taskElement, "StackSize")) ?? 1024,
                    EntryFunction = GetElementValue(taskElement, "EntryPoint") ?? GetElementValue(taskElement, "TaskMain"),
                    Autostart = ParseBool(GetElementValue(taskElement, "Autostart")) ?? false
                };
                
                config.Tasks.Add(task);
            }
        }

        /// <summary>
        /// 从XML解析中断配置
        /// </summary>
        private void ParseInterruptsFromXml(XDocument doc, AutosarConfiguration config)
        {
            var isrElements = doc.Descendants()
                .Where(e => e.Name.LocalName == "ISR" || e.Name.LocalName == "OsIsr");
            
            foreach (var isrElement in isrElements)
            {
                var interrupt = new AutosarInterrupt
                {
                    Name = GetAttributeValue(isrElement, "name") ?? GetElementValue(isrElement, "Name"),
                    VectorNumber = ParseInt(GetElementValue(isrElement, "Vector")) ?? 0,
                    Priority = ParseInt(GetElementValue(isrElement, "Priority")) ?? 0,
                    HandlerFunction = GetElementValue(isrElement, "Handler") ?? GetElementValue(isrElement, "IsrMain"),
                    Category = ParseInt(GetElementValue(isrElement, "Category")) ?? 2
                };
                
                config.Interrupts.Add(interrupt);
            }
        }

        /// <summary>
        /// 从XML解析堆栈配置
        /// </summary>
        private void ParseStackConfigFromXml(XDocument doc, AutosarConfiguration config)
        {
            var stackElements = doc.Descendants()
                .Where(e => e.Name.LocalName == "Stack" || e.Name.LocalName == "OsStack");
            
            foreach (var stackElement in stackElements)
            {
                var stackConfig = new StackConfiguration
                {
                    Name = GetAttributeValue(stackElement, "name") ?? GetElementValue(stackElement, "Name"),
                    Size = ParseInt(GetElementValue(stackElement, "Size")) ?? 1024,
                    Type = GetElementValue(stackElement, "Type") ?? "Task"
                };
                
                config.StackConfigurations.Add(stackConfig);
            }
        }

        /// <summary>
        /// 从OIL格式解析任务
        /// </summary>
        private void ParseTasksFromOil(string content, AutosarConfiguration config)
        {
            var taskPattern = @"TASK\s+(\w+)\s*\{([^}]+)\}";
            var matches = System.Text.RegularExpressions.Regex.Matches(content, taskPattern, 
                System.Text.RegularExpressions.RegexOptions.Multiline | System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            
            foreach (System.Text.RegularExpressions.Match match in matches)
            {
                string taskName = match.Groups[1].Value;
                string taskBody = match.Groups[2].Value;
                
                var task = new AutosarTask
                {
                    Name = taskName,
                    Priority = ExtractOilValue(taskBody, "PRIORITY", 0),
                    StackSize = ExtractOilValue(taskBody, "STACKSIZE", 1024),
                    Autostart = ExtractOilBool(taskBody, "AUTOSTART", false)
                };
                
                config.Tasks.Add(task);
            }
        }

        /// <summary>
        /// 从OIL格式解析中断
        /// </summary>
        private void ParseInterruptsFromOil(string content, AutosarConfiguration config)
        {
            var isrPattern = @"ISR\s+(\w+)\s*\{([^}]+)\}";
            var matches = System.Text.RegularExpressions.Regex.Matches(content, isrPattern, 
                System.Text.RegularExpressions.RegexOptions.Multiline | System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            
            foreach (System.Text.RegularExpressions.Match match in matches)
            {
                string isrName = match.Groups[1].Value;
                string isrBody = match.Groups[2].Value;
                
                var interrupt = new AutosarInterrupt
                {
                    Name = isrName,
                    VectorNumber = ExtractOilValue(isrBody, "VECTOR", 0),
                    Priority = ExtractOilValue(isrBody, "PRIORITY", 0),
                    Category = ExtractOilValue(isrBody, "CATEGORY", 2)
                };
                
                config.Interrupts.Add(interrupt);
            }
        }

        /// <summary>
        /// 从OIL格式解析堆栈配置
        /// </summary>
        private void ParseStackConfigFromOil(string content, AutosarConfiguration config)
        {
            // OIL格式中堆栈配置通常在OS对象中
            var osPattern = @"OS\s+\w+\s*\{([^}]+)\}";
            var match = System.Text.RegularExpressions.Regex.Match(content, osPattern, 
                System.Text.RegularExpressions.RegexOptions.Multiline | System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            
            if (match.Success)
            {
                string osBody = match.Groups[1].Value;
                
                var stackConfig = new StackConfiguration
                {
                    Name = "SystemStack",
                    Size = ExtractOilValue(osBody, "STACKSIZE", 2048),
                    Type = "System"
                };
                
                config.StackConfigurations.Add(stackConfig);
            }
        }

        /// <summary>
        /// 从C头文件解析任务配置
        /// </summary>
        private void ParseTasksFromC(string content, AutosarConfiguration config)
        {
            // 解析任务相关的宏定义
            var taskDefines = ExtractDefines(content, "TASK_");
            
            foreach (var define in taskDefines)
            {
                if (define.Key.EndsWith("_PRIORITY"))
                {
                    string taskName = define.Key.Replace("TASK_", "").Replace("_PRIORITY", "");
                    var task = config.Tasks.FirstOrDefault(t => t.Name == taskName) ?? new AutosarTask { Name = taskName };
                    
                    if (int.TryParse(define.Value, out int priority))
                        task.Priority = priority;
                    
                    if (!config.Tasks.Contains(task))
                        config.Tasks.Add(task);
                }
                else if (define.Key.EndsWith("_STACKSIZE"))
                {
                    string taskName = define.Key.Replace("TASK_", "").Replace("_STACKSIZE", "");
                    var task = config.Tasks.FirstOrDefault(t => t.Name == taskName) ?? new AutosarTask { Name = taskName };
                    
                    if (int.TryParse(define.Value, out int stackSize))
                        task.StackSize = stackSize;
                    
                    if (!config.Tasks.Contains(task))
                        config.Tasks.Add(task);
                }
            }
        }

        /// <summary>
        /// 从C头文件解析中断配置
        /// </summary>
        private void ParseInterruptsFromC(string content, AutosarConfiguration config)
        {
            var isrDefines = ExtractDefines(content, "ISR_");
            
            foreach (var define in isrDefines)
            {
                if (define.Key.EndsWith("_VECTOR"))
                {
                    string isrName = define.Key.Replace("ISR_", "").Replace("_VECTOR", "");
                    var interrupt = config.Interrupts.FirstOrDefault(i => i.Name == isrName) ?? new AutosarInterrupt { Name = isrName };
                    
                    if (int.TryParse(define.Value, out int vector))
                        interrupt.VectorNumber = vector;
                    
                    if (!config.Interrupts.Contains(interrupt))
                        config.Interrupts.Add(interrupt);
                }
                else if (define.Key.EndsWith("_PRIORITY"))
                {
                    string isrName = define.Key.Replace("ISR_", "").Replace("_PRIORITY", "");
                    var interrupt = config.Interrupts.FirstOrDefault(i => i.Name == isrName) ?? new AutosarInterrupt { Name = isrName };
                    
                    if (int.TryParse(define.Value, out int priority))
                        interrupt.Priority = priority;
                    
                    if (!config.Interrupts.Contains(interrupt))
                        config.Interrupts.Add(interrupt);
                }
            }
        }

        /// <summary>
        /// 从C头文件解析堆栈配置
        /// </summary>
        private void ParseStackConfigFromC(string content, AutosarConfiguration config)
        {
            var stackDefines = ExtractDefines(content, "STACK_");
            
            foreach (var define in stackDefines)
            {
                if (define.Key.EndsWith("_SIZE"))
                {
                    string stackName = define.Key.Replace("STACK_", "").Replace("_SIZE", "");
                    
                    if (int.TryParse(define.Value, out int size))
                    {
                        var stackConfig = new StackConfiguration
                        {
                            Name = stackName,
                            Size = size,
                            Type = "Task"
                        };
                        
                        config.StackConfigurations.Add(stackConfig);
                    }
                }
            }
        }

        // 辅助方法
        private string GetAttributeValue(XElement element, string attributeName)
        {
            return element.Attribute(attributeName)?.Value;
        }

        private string GetElementValue(XElement element, string elementName)
        {
            return element.Descendants().FirstOrDefault(e => e.Name.LocalName == elementName)?.Value;
        }

        private int? ParseInt(string value)
        {
            return int.TryParse(value, out int result) ? result : (int?)null;
        }

        private bool? ParseBool(string value)
        {
            return bool.TryParse(value, out bool result) ? result : (bool?)null;
        }

        private int ExtractOilValue(string content, string key, int defaultValue)
        {
            var pattern = $@"{key}\s*=\s*(\d+)";
            var match = System.Text.RegularExpressions.Regex.Match(content, pattern, System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            
            return match.Success && int.TryParse(match.Groups[1].Value, out int value) ? value : defaultValue;
        }

        private bool ExtractOilBool(string content, string key, bool defaultValue)
        {
            var pattern = $@"{key}\s*=\s*(TRUE|FALSE)";
            var match = System.Text.RegularExpressions.Regex.Match(content, pattern, System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            
            return match.Success ? match.Groups[1].Value.ToUpper() == "TRUE" : defaultValue;
        }

        private Dictionary<string, string> ExtractDefines(string content, string prefix)
        {
            var defines = new Dictionary<string, string>();
            var pattern = $@"#define\s+({prefix}\w+)\s+(.+)";
            var matches = System.Text.RegularExpressions.Regex.Matches(content, pattern);
            
            foreach (System.Text.RegularExpressions.Match match in matches)
            {
                defines[match.Groups[1].Value] = match.Groups[2].Value.Trim();
            }
            
            return defines;
        }
    }

    /// <summary>
    /// AUTOSAR配置信息
    /// </summary>
    public class AutosarConfiguration
    {
        public List<AutosarTask> Tasks { get; set; }
        public List<AutosarInterrupt> Interrupts { get; set; }
        public List<StackConfiguration> StackConfigurations { get; set; }

        public AutosarConfiguration()
        {
            Tasks = new List<AutosarTask>();
            Interrupts = new List<AutosarInterrupt>();
            StackConfigurations = new List<StackConfiguration>();
        }
    }

    /// <summary>
    /// AUTOSAR任务配置
    /// </summary>
    public class AutosarTask
    {
        public string Name { get; set; }
        public string Id { get; set; }
        public int Priority { get; set; }
        public int StackSize { get; set; }
        public string EntryFunction { get; set; }
        public bool Autostart { get; set; }
    }

    /// <summary>
    /// AUTOSAR中断配置
    /// </summary>
    public class AutosarInterrupt
    {
        public string Name { get; set; }
        public int VectorNumber { get; set; }
        public int Priority { get; set; }
        public string HandlerFunction { get; set; }
        public int Category { get; set; } // 1 or 2
    }

    /// <summary>
    /// 堆栈配置
    /// </summary>
    public class StackConfiguration
    {
        public string Name { get; set; }
        public int Size { get; set; }
        public string Type { get; set; } // Task, ISR, System
    }
}
