using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using CalculateStackSize.Models;
using CalculateStackSize.Core.Parser;

namespace CalculateStackSize.Core.Analysis
{
    /// <summary>
    /// 调用图分析器
    /// </summary>
    public class CallGraphAnalyzer
    {
        private readonly CParser _cParser;
        private readonly AutosarConfigParser _autosarParser;
        private CallGraph _callGraph;

        public CallGraphAnalyzer()
        {
            _cParser = new CParser();
            _autosarParser = new AutosarConfigParser();
            _callGraph = new CallGraph();
        }

        /// <summary>
        /// 分析项目并构建调用图
        /// </summary>
        public CallGraph AnalyzeProject(string projectPath, string autosarConfigPath = null)
        {
            _callGraph = new CallGraph();
            
            try
            {
                // 1. 解析所有C源文件
                var sourceFiles = FindSourceFiles(projectPath);
                var allFunctions = new List<Function>();
                
                foreach (var sourceFile in sourceFiles)
                {
                    Console.WriteLine($"Parsing file: {sourceFile}");
                    var functions = _cParser.ParseSourceFile(sourceFile);
                    allFunctions.AddRange(functions);
                }
                
                // 2. 将函数添加到调用图
                foreach (var function in allFunctions)
                {
                    _callGraph.AddFunction(function);
                }
                
                // 3. 解析AUTOSAR配置（如果提供）
                AutosarConfiguration autosarConfig = null;
                if (!string.IsNullOrEmpty(autosarConfigPath) && File.Exists(autosarConfigPath))
                {
                    autosarConfig = _autosarParser.ParseConfiguration(autosarConfigPath);
                    ApplyAutosarConfiguration(autosarConfig);
                }
                
                // 4. 解析函数调用关系
                ResolveFunctionCalls();
                
                // 5. 识别入口点
                IdentifyEntryPoints(autosarConfig);
                
                // 6. 检测递归调用
                _callGraph.DetectRecursion();
                
                // 7. 查找孤立函数
                _callGraph.FindOrphanFunctions();
                
                Console.WriteLine($"Analysis completed. Found {_callGraph.Functions.Count} functions.");
                
                return _callGraph;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error analyzing project: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 查找项目中的所有C源文件
        /// </summary>
        private List<string> FindSourceFiles(string projectPath)
        {
            var sourceFiles = new List<string>();
            
            if (File.Exists(projectPath))
            {
                // 单个文件
                sourceFiles.Add(projectPath);
            }
            else if (Directory.Exists(projectPath))
            {
                // 目录，递归查找所有.c和.cpp文件
                var extensions = new[] { "*.c", "*.cpp", "*.cc" };
                
                foreach (var extension in extensions)
                {
                    sourceFiles.AddRange(Directory.GetFiles(projectPath, extension, SearchOption.AllDirectories));
                }
            }
            else
            {
                throw new DirectoryNotFoundException($"Project path not found: {projectPath}");
            }
            
            return sourceFiles.Where(f => !IsExcludedFile(f)).ToList();
        }

        /// <summary>
        /// 检查是否为排除的文件
        /// </summary>
        private bool IsExcludedFile(string filePath)
        {
            var excludePatterns = new[]
            {
                "test", "Test", "TEST",
                "mock", "Mock", "MOCK",
                "stub", "Stub", "STUB"
            };
            
            string fileName = Path.GetFileName(filePath);
            return excludePatterns.Any(pattern => fileName.Contains(pattern));
        }

        /// <summary>
        /// 应用AUTOSAR配置
        /// </summary>
        private void ApplyAutosarConfiguration(AutosarConfiguration config)
        {
            // 标记任务函数
            foreach (var task in config.Tasks)
            {
                var function = _callGraph.FindFunction(task.EntryFunction);
                if (function != null)
                {
                    function.Type = FunctionType.TaskFunction;
                    function.AutosarTaskId = task.Id ?? task.Name;
                }
            }
            
            // 标记中断服务例程
            foreach (var interrupt in config.Interrupts)
            {
                var function = _callGraph.FindFunction(interrupt.HandlerFunction);
                if (function != null)
                {
                    function.Type = FunctionType.InterruptHandler;
                    function.InterruptPriority = interrupt.Priority;
                }
            }
        }

        /// <summary>
        /// 解析函数调用关系
        /// </summary>
        private void ResolveFunctionCalls()
        {
            foreach (var function in _callGraph.Functions.Values)
            {
                // 解析每个函数调用的目标函数
                for (int i = 0; i < function.CalledFunctions.Count; i++)
                {
                    var call = function.CalledFunctions[i];
                    var targetFunction = _callGraph.FindFunction(call.Function.Name);
                    
                    if (targetFunction != null)
                    {
                        // 更新调用关系
                        call.Function = targetFunction;
                        
                        // 添加反向引用
                        if (!targetFunction.CallerFunctions.Contains(function))
                        {
                            targetFunction.CallerFunctions.Add(function);
                        }
                    }
                    else
                    {
                        // 未找到目标函数，可能是库函数或外部函数
                        call.CallType = CallType.Indirect;
                        Console.WriteLine($"Warning: Function '{call.Function.Name}' called by '{function.Name}' not found in source code.");
                    }
                }
            }
        }

        /// <summary>
        /// 识别入口点函数
        /// </summary>
        private void IdentifyEntryPoints(AutosarConfiguration autosarConfig)
        {
            // 1. 从AUTOSAR配置中识别入口点
            if (autosarConfig != null)
            {
                // 任务入口点
                foreach (var task in autosarConfig.Tasks)
                {
                    var function = _callGraph.FindFunction(task.EntryFunction);
                    if (function != null)
                    {
                        _callGraph.AddEntryPoint(function);
                    }
                }
                
                // 中断入口点
                foreach (var interrupt in autosarConfig.Interrupts)
                {
                    var function = _callGraph.FindFunction(interrupt.HandlerFunction);
                    if (function != null)
                    {
                        _callGraph.AddEntryPoint(function);
                    }
                }
            }
            
            // 2. 根据函数类型识别入口点
            foreach (var function in _callGraph.Functions.Values)
            {
                if (function.Type == FunctionType.TaskFunction || 
                    function.Type == FunctionType.InterruptHandler)
                {
                    _callGraph.AddEntryPoint(function);
                }
            }
            
            // 3. 识别main函数
            var mainFunction = _callGraph.FindFunction("main");
            if (mainFunction != null)
            {
                _callGraph.AddEntryPoint(mainFunction);
            }
            
            // 4. 识别没有调用者的函数（可能的入口点）
            foreach (var function in _callGraph.Functions.Values)
            {
                if (function.CallerFunctions.Count == 0 && 
                    function.Type != FunctionType.Normal)
                {
                    _callGraph.AddEntryPoint(function);
                }
            }
        }

        /// <summary>
        /// 分析函数的调用深度
        /// </summary>
        public Dictionary<Function, int> AnalyzeCallDepth()
        {
            var callDepths = new Dictionary<Function, int>();
            
            foreach (var entryPoint in _callGraph.EntryPoints)
            {
                var visited = new HashSet<Function>();
                int maxDepth = CalculateMaxDepth(entryPoint, visited, 0);
                callDepths[entryPoint] = maxDepth;
            }
            
            return callDepths;
        }

        /// <summary>
        /// 计算从指定函数开始的最大调用深度
        /// </summary>
        private int CalculateMaxDepth(Function function, HashSet<Function> visited, int currentDepth)
        {
            if (visited.Contains(function))
            {
                // 检测到递归调用
                return currentDepth + function.MaxRecursionDepth;
            }
            
            visited.Add(function);
            int maxDepth = currentDepth;
            
            foreach (var call in function.CalledFunctions)
            {
                int depth = CalculateMaxDepth(call.Function, visited, currentDepth + 1);
                maxDepth = Math.Max(maxDepth, depth);
            }
            
            visited.Remove(function);
            return maxDepth;
        }

        /// <summary>
        /// 查找两个函数之间的所有调用路径
        /// </summary>
        public List<CallPath> FindCallPaths(Function from, Function to, int maxDepth = 50)
        {
            var paths = new List<CallPath>();
            var currentPath = new CallPath();
            var visited = new HashSet<Function>();
            
            FindCallPathsRecursive(from, to, currentPath, paths, visited, maxDepth);
            
            return paths;
        }

        /// <summary>
        /// 递归查找调用路径
        /// </summary>
        private void FindCallPathsRecursive(Function current, Function target, CallPath currentPath, 
            List<CallPath> allPaths, HashSet<Function> visited, int remainingDepth)
        {
            if (remainingDepth <= 0 || visited.Contains(current))
                return;
            
            visited.Add(current);
            currentPath.Functions.Add(current);
            
            if (current == target)
            {
                // 找到目标函数，添加路径
                allPaths.Add(new CallPath(currentPath));
            }
            else
            {
                // 继续搜索
                foreach (var call in current.CalledFunctions)
                {
                    FindCallPathsRecursive(call.Function, target, currentPath, allPaths, visited, remainingDepth - 1);
                }
            }
            
            currentPath.Functions.RemoveAt(currentPath.Functions.Count - 1);
            visited.Remove(current);
        }

        /// <summary>
        /// 分析函数的复杂度
        /// </summary>
        public Dictionary<Function, FunctionComplexity> AnalyzeFunctionComplexity()
        {
            var complexities = new Dictionary<Function, FunctionComplexity>();
            
            foreach (var function in _callGraph.Functions.Values)
            {
                var complexity = new FunctionComplexity
                {
                    Function = function,
                    CallCount = function.CalledFunctions.Count,
                    CallerCount = function.CallerFunctions.Count,
                    LocalVariableCount = function.LocalVariables.Count,
                    ParameterCount = function.Parameters.Count,
                    IsRecursive = function.IsRecursive,
                    ComplexityScore = CalculateComplexityScore(function)
                };
                
                complexities[function] = complexity;
            }
            
            return complexities;
        }

        /// <summary>
        /// 计算函数复杂度分数
        /// </summary>
        private int CalculateComplexityScore(Function function)
        {
            int score = 0;
            
            // 基于调用数量
            score += function.CalledFunctions.Count * 2;
            
            // 基于局部变量数量
            score += function.LocalVariables.Count;
            
            // 基于参数数量
            score += function.Parameters.Count;
            
            // 递归函数额外分数
            if (function.IsRecursive)
                score += 10;
            
            // 中断处理函数额外分数
            if (function.Type == FunctionType.InterruptHandler)
                score += 5;
            
            return score;
        }

        /// <summary>
        /// 获取调用图的统计信息
        /// </summary>
        public CallGraphStatistics GetStatistics()
        {
            return _callGraph.GetStatistics();
        }
    }

    /// <summary>
    /// 函数复杂度信息
    /// </summary>
    public class FunctionComplexity
    {
        public Function Function { get; set; }
        public int CallCount { get; set; }
        public int CallerCount { get; set; }
        public int LocalVariableCount { get; set; }
        public int ParameterCount { get; set; }
        public bool IsRecursive { get; set; }
        public int ComplexityScore { get; set; }
    }
}
