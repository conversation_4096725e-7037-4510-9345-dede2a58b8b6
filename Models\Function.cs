using System;
using System.Collections.Generic;
using System.Linq;

namespace CalculateStackSize.Models
{
    /// <summary>
    /// 表示一个函数的信息
    /// </summary>
    public class Function
    {
        public string Name { get; set; }
        public string SourceFile { get; set; }
        public int LineNumber { get; set; }
        public FunctionType Type { get; set; }
        
        /// <summary>
        /// 函数的局部变量大小（字节）
        /// </summary>
        public int LocalVariableSize { get; set; }
        
        /// <summary>
        /// 函数参数大小（字节）
        /// </summary>
        public int ParameterSize { get; set; }
        
        /// <summary>
        /// 寄存器保存大小（字节）
        /// </summary>
        public int RegisterSaveSize { get; set; }
        
        /// <summary>
        /// 函数调用的其他函数
        /// </summary>
        public List<FunctionCall> CalledFunctions { get; set; }
        
        /// <summary>
        /// 调用此函数的其他函数
        /// </summary>
        public List<Function> CallerFunctions { get; set; }
        
        /// <summary>
        /// 函数的局部变量信息
        /// </summary>
        public List<LocalVariable> LocalVariables { get; set; }
        
        /// <summary>
        /// 函数参数信息
        /// </summary>
        public List<Parameter> Parameters { get; set; }
        
        /// <summary>
        /// 是否为递归函数
        /// </summary>
        public bool IsRecursive { get; set; }
        
        /// <summary>
        /// 最大递归深度（如果是递归函数）
        /// </summary>
        public int MaxRecursionDepth { get; set; }
        
        /// <summary>
        /// 中断优先级（如果是中断服务例程）
        /// </summary>
        public int? InterruptPriority { get; set; }
        
        /// <summary>
        /// AUTOSAR任务ID（如果是任务函数）
        /// </summary>
        public string AutosarTaskId { get; set; }

        public Function()
        {
            CalledFunctions = new List<FunctionCall>();
            CallerFunctions = new List<Function>();
            LocalVariables = new List<LocalVariable>();
            Parameters = new List<Parameter>();
            MaxRecursionDepth = 1;
        }

        /// <summary>
        /// 计算函数的基础堆栈大小（不包括调用链）
        /// </summary>
        public int GetBaseStackSize()
        {
            return LocalVariableSize + ParameterSize + RegisterSaveSize;
        }

        /// <summary>
        /// 添加被调用的函数
        /// </summary>
        public void AddCalledFunction(Function function, int lineNumber, CallType callType = CallType.Direct)
        {
            var call = new FunctionCall
            {
                Function = function,
                LineNumber = lineNumber,
                CallType = callType
            };
            CalledFunctions.Add(call);
            
            if (!function.CallerFunctions.Contains(this))
            {
                function.CallerFunctions.Add(this);
            }
        }

        public override string ToString()
        {
            return $"{Name} ({Type}) - Base Stack: {GetBaseStackSize()} bytes";
        }

        public override bool Equals(object obj)
        {
            if (obj is Function other)
            {
                return Name == other.Name && SourceFile == other.SourceFile;
            }
            return false;
        }

        public override int GetHashCode()
        {
            return (Name + SourceFile).GetHashCode();
        }
    }

    /// <summary>
    /// 函数类型枚举
    /// </summary>
    public enum FunctionType
    {
        Normal,           // 普通函数
        InterruptHandler, // 中断服务例程
        TaskFunction,     // AUTOSAR任务函数
        HookFunction,     // AUTOSAR钩子函数
        CallbackFunction  // 回调函数
    }

    /// <summary>
    /// 函数调用信息
    /// </summary>
    public class FunctionCall
    {
        public Function Function { get; set; }
        public int LineNumber { get; set; }
        public CallType CallType { get; set; }
        
        /// <summary>
        /// 调用时的额外堆栈开销（如函数指针调用）
        /// </summary>
        public int AdditionalStackCost { get; set; }
    }

    /// <summary>
    /// 调用类型枚举
    /// </summary>
    public enum CallType
    {
        Direct,      // 直接调用
        Indirect,    // 间接调用（函数指针）
        Recursive,   // 递归调用
        Interrupt    // 中断调用
    }

    /// <summary>
    /// 局部变量信息
    /// </summary>
    public class LocalVariable
    {
        public string Name { get; set; }
        public string Type { get; set; }
        public int Size { get; set; }
        public int Offset { get; set; }
        public bool IsArray { get; set; }
        public int ArraySize { get; set; }
    }

    /// <summary>
    /// 函数参数信息
    /// </summary>
    public class Parameter
    {
        public string Name { get; set; }
        public string Type { get; set; }
        public int Size { get; set; }
        public ParameterPassingMethod PassingMethod { get; set; }
    }

    /// <summary>
    /// 参数传递方式
    /// </summary>
    public enum ParameterPassingMethod
    {
        Register,    // 寄存器传递
        Stack,       // 堆栈传递
        Mixed        // 混合传递
    }
}
