using System;
using System.Collections.Generic;
using System.Linq;

namespace CalculateStackSize.Models
{
    /// <summary>
    /// 堆栈帧信息
    /// </summary>
    public class StackFrame
    {
        /// <summary>
        /// 对应的函数
        /// </summary>
        public Function Function { get; set; }
        
        /// <summary>
        /// 帧大小（字节）
        /// </summary>
        public int FrameSize { get; set; }
        
        /// <summary>
        /// 局部变量区域大小
        /// </summary>
        public int LocalVariablesSize { get; set; }
        
        /// <summary>
        /// 参数区域大小
        /// </summary>
        public int ParametersSize { get; set; }
        
        /// <summary>
        /// 保存的寄存器大小
        /// </summary>
        public int SavedRegistersSize { get; set; }
        
        /// <summary>
        /// 返回地址大小
        /// </summary>
        public int ReturnAddressSize { get; set; }
        
        /// <summary>
        /// 帧指针大小
        /// </summary>
        public int FramePointerSize { get; set; }
        
        /// <summary>
        /// 对齐填充大小
        /// </summary>
        public int AlignmentPadding { get; set; }
        
        /// <summary>
        /// 堆栈帧布局
        /// </summary>
        public List<StackFrameRegion> Layout { get; set; }
        
        /// <summary>
        /// 处理器架构
        /// </summary>
        public ProcessorArchitecture Architecture { get; set; }

        public StackFrame(Function function, ProcessorArchitecture architecture)
        {
            Function = function;
            Architecture = architecture;
            Layout = new List<StackFrameRegion>();
            CalculateFrameSize();
        }

        /// <summary>
        /// 计算堆栈帧大小
        /// </summary>
        public void CalculateFrameSize()
        {
            Layout.Clear();
            
            // 根据处理器架构计算各部分大小
            switch (Architecture)
            {
                case ProcessorArchitecture.RH850:
                    CalculateRH850FrameSize();
                    break;
                case ProcessorArchitecture.TC377:
                    CalculateTC377FrameSize();
                    break;
                default:
                    CalculateGenericFrameSize();
                    break;
            }
            
            // 计算总帧大小
            FrameSize = Layout.Sum(region => region.Size);
        }

        /// <summary>
        /// 计算RH850架构的堆栈帧大小
        /// </summary>
        private void CalculateRH850FrameSize()
        {
            int offset = 0;
            
            // RH850 V850架构特定的堆栈布局
            // 1. 返回地址 (4字节)
            ReturnAddressSize = 4;
            Layout.Add(new StackFrameRegion
            {
                Type = StackRegionType.ReturnAddress,
                Offset = offset,
                Size = ReturnAddressSize,
                Description = "Return Address"
            });
            offset += ReturnAddressSize;
            
            // 2. 保存的寄存器
            SavedRegistersSize = CalculateRH850RegisterSaveSize();
            if (SavedRegistersSize > 0)
            {
                Layout.Add(new StackFrameRegion
                {
                    Type = StackRegionType.SavedRegisters,
                    Offset = offset,
                    Size = SavedRegistersSize,
                    Description = "Saved Registers"
                });
                offset += SavedRegistersSize;
            }
            
            // 3. 局部变量
            LocalVariablesSize = Function.LocalVariableSize;
            if (LocalVariablesSize > 0)
            {
                Layout.Add(new StackFrameRegion
                {
                    Type = StackRegionType.LocalVariables,
                    Offset = offset,
                    Size = LocalVariablesSize,
                    Description = "Local Variables"
                });
                offset += LocalVariablesSize;
            }
            
            // 4. 参数传递区域（如果需要）
            ParametersSize = CalculateParameterStackSize();
            if (ParametersSize > 0)
            {
                Layout.Add(new StackFrameRegion
                {
                    Type = StackRegionType.Parameters,
                    Offset = offset,
                    Size = ParametersSize,
                    Description = "Function Parameters"
                });
                offset += ParametersSize;
            }
            
            // 5. 对齐填充
            AlignmentPadding = CalculateAlignmentPadding(offset, 4); // RH850 4字节对齐
            if (AlignmentPadding > 0)
            {
                Layout.Add(new StackFrameRegion
                {
                    Type = StackRegionType.Padding,
                    Offset = offset,
                    Size = AlignmentPadding,
                    Description = "Alignment Padding"
                });
            }
        }

        /// <summary>
        /// 计算TC377架构的堆栈帧大小
        /// </summary>
        private void CalculateTC377FrameSize()
        {
            int offset = 0;
            
            // TC377 TriCore架构特定的堆栈布局
            // 1. 返回地址 (4字节)
            ReturnAddressSize = 4;
            Layout.Add(new StackFrameRegion
            {
                Type = StackRegionType.ReturnAddress,
                Offset = offset,
                Size = ReturnAddressSize,
                Description = "Return Address"
            });
            offset += ReturnAddressSize;
            
            // 2. 上下文保存区域
            SavedRegistersSize = CalculateTC377RegisterSaveSize();
            if (SavedRegistersSize > 0)
            {
                Layout.Add(new StackFrameRegion
                {
                    Type = StackRegionType.SavedRegisters,
                    Offset = offset,
                    Size = SavedRegistersSize,
                    Description = "Saved Context"
                });
                offset += SavedRegistersSize;
            }
            
            // 3. 局部变量
            LocalVariablesSize = Function.LocalVariableSize;
            if (LocalVariablesSize > 0)
            {
                Layout.Add(new StackFrameRegion
                {
                    Type = StackRegionType.LocalVariables,
                    Offset = offset,
                    Size = LocalVariablesSize,
                    Description = "Local Variables"
                });
                offset += LocalVariablesSize;
            }
            
            // 4. 参数传递区域
            ParametersSize = CalculateParameterStackSize();
            if (ParametersSize > 0)
            {
                Layout.Add(new StackFrameRegion
                {
                    Type = StackRegionType.Parameters,
                    Offset = offset,
                    Size = ParametersSize,
                    Description = "Function Parameters"
                });
                offset += ParametersSize;
            }
            
            // 5. 对齐填充
            AlignmentPadding = CalculateAlignmentPadding(offset, 8); // TC377 8字节对齐
            if (AlignmentPadding > 0)
            {
                Layout.Add(new StackFrameRegion
                {
                    Type = StackRegionType.Padding,
                    Offset = offset,
                    Size = AlignmentPadding,
                    Description = "Alignment Padding"
                });
            }
        }

        /// <summary>
        /// 计算通用架构的堆栈帧大小
        /// </summary>
        private void CalculateGenericFrameSize()
        {
            ReturnAddressSize = 4;
            SavedRegistersSize = Function.RegisterSaveSize;
            LocalVariablesSize = Function.LocalVariableSize;
            ParametersSize = Function.ParameterSize;
            AlignmentPadding = 0;
        }

        /// <summary>
        /// 计算RH850寄存器保存大小
        /// </summary>
        private int CalculateRH850RegisterSaveSize()
        {
            // RH850 V850架构的寄存器保存规则
            // 根据函数调用约定，某些寄存器需要保存
            int saveSize = 0;
            
            // 基本的callee-saved寄存器
            saveSize += 4 * 4; // r20-r23 (4个寄存器 * 4字节)
            
            // 如果函数使用了浮点运算，需要保存浮点寄存器
            // 这里简化处理，实际需要根据函数内容分析
            
            return saveSize;
        }

        /// <summary>
        /// 计算TC377寄存器保存大小
        /// </summary>
        private int CalculateTC377RegisterSaveSize()
        {
            // TC377 TriCore架构的寄存器保存规则
            int saveSize = 0;
            
            // 基本的上下文保存
            saveSize += 4 * 8; // 8个通用寄存器 * 4字节
            
            return saveSize;
        }

        /// <summary>
        /// 计算参数在堆栈上的大小
        /// </summary>
        private int CalculateParameterStackSize()
        {
            return Function.Parameters
                .Where(p => p.PassingMethod == ParameterPassingMethod.Stack)
                .Sum(p => p.Size);
        }

        /// <summary>
        /// 计算对齐填充
        /// </summary>
        private int CalculateAlignmentPadding(int currentSize, int alignment)
        {
            int remainder = currentSize % alignment;
            return remainder == 0 ? 0 : alignment - remainder;
        }

        public override string ToString()
        {
            return $"{Function.Name} Stack Frame: {FrameSize} bytes";
        }
    }

    /// <summary>
    /// 堆栈帧区域
    /// </summary>
    public class StackFrameRegion
    {
        public StackRegionType Type { get; set; }
        public int Offset { get; set; }
        public int Size { get; set; }
        public string Description { get; set; }
    }

    /// <summary>
    /// 堆栈区域类型
    /// </summary>
    public enum StackRegionType
    {
        ReturnAddress,    // 返回地址
        SavedRegisters,   // 保存的寄存器
        LocalVariables,   // 局部变量
        Parameters,       // 函数参数
        Padding          // 对齐填充
    }

    /// <summary>
    /// 处理器架构枚举
    /// </summary>
    public enum ProcessorArchitecture
    {
        Generic,  // 通用架构
        RH850,    // Renesas RH850
        TC377     // Infineon TC377
    }
}
