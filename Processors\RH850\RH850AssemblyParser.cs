using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using CalculateStackSize.Models;

namespace CalculateStackSize.Processors.RH850
{
    /// <summary>
    /// RH850汇编代码解析器
    /// </summary>
    public class RH850AssemblyParser
    {
        private readonly RH850Processor _processor;
        private readonly Dictionary<string, RH850Instruction> _instructionSet;

        public RH850AssemblyParser()
        {
            _processor = new RH850Processor();
            _instructionSet = InitializeInstructionSet();
        }

        /// <summary>
        /// 解析汇编文件
        /// </summary>
        public List<AssemblyFunction> ParseAssemblyFile(string filePath)
        {
            if (!File.Exists(filePath))
                throw new FileNotFoundException($"Assembly file not found: {filePath}");

            string content = File.ReadAllText(filePath);
            return ParseAssemblyContent(content, filePath);
        }

        /// <summary>
        /// 解析汇编内容
        /// </summary>
        public List<AssemblyFunction> ParseAssemblyContent(string content, string sourceFile)
        {
            var functions = new List<AssemblyFunction>();
            var lines = content.Split('\n');
            
            AssemblyFunction currentFunction = null;
            
            for (int i = 0; i < lines.Length; i++)
            {
                string line = lines[i].Trim();
                
                if (string.IsNullOrEmpty(line) || line.StartsWith(";"))
                    continue; // 跳过空行和注释
                
                // 检查函数标签
                if (IsFunctionLabel(line))
                {
                    if (currentFunction != null)
                    {
                        functions.Add(currentFunction);
                    }
                    
                    currentFunction = new AssemblyFunction
                    {
                        Name = ExtractFunctionName(line),
                        SourceFile = sourceFile,
                        StartLine = i + 1,
                        Instructions = new List<AssemblyInstruction>()
                    };
                }
                else if (currentFunction != null && IsInstruction(line))
                {
                    var instruction = ParseInstruction(line, i + 1);
                    if (instruction != null)
                    {
                        currentFunction.Instructions.Add(instruction);
                        
                        // 分析堆栈影响
                        AnalyzeStackImpact(instruction, currentFunction);
                    }
                }
            }
            
            if (currentFunction != null)
            {
                functions.Add(currentFunction);
            }
            
            return functions;
        }

        /// <summary>
        /// 检查是否为函数标签
        /// </summary>
        private bool IsFunctionLabel(string line)
        {
            // RH850汇编中函数标签通常以冒号结尾
            return Regex.IsMatch(line, @"^[a-zA-Z_][a-zA-Z0-9_]*:$");
        }

        /// <summary>
        /// 提取函数名
        /// </summary>
        private string ExtractFunctionName(string line)
        {
            return line.TrimEnd(':');
        }

        /// <summary>
        /// 检查是否为指令
        /// </summary>
        private bool IsInstruction(string line)
        {
            // 检查是否以已知的指令助记符开始
            var parts = line.Split(new[] { ' ', '\t' }, StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length == 0) return false;
            
            string mnemonic = parts[0].ToLower();
            return _instructionSet.ContainsKey(mnemonic);
        }

        /// <summary>
        /// 解析指令
        /// </summary>
        private AssemblyInstruction ParseInstruction(string line, int lineNumber)
        {
            var parts = line.Split(new[] { ' ', '\t' }, StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length == 0) return null;
            
            string mnemonic = parts[0].ToLower();
            string operands = parts.Length > 1 ? string.Join(" ", parts.Skip(1)) : "";
            
            if (!_instructionSet.ContainsKey(mnemonic))
                return null;
            
            var instructionInfo = _instructionSet[mnemonic];
            
            return new AssemblyInstruction
            {
                Mnemonic = mnemonic,
                Operands = operands,
                LineNumber = lineNumber,
                OriginalText = line,
                InstructionType = instructionInfo.Type,
                StackImpact = CalculateInstructionStackImpact(mnemonic, operands)
            };
        }

        /// <summary>
        /// 分析堆栈影响
        /// </summary>
        private void AnalyzeStackImpact(AssemblyInstruction instruction, AssemblyFunction function)
        {
            function.TotalStackImpact += instruction.StackImpact;
            
            // 跟踪最大堆栈使用
            if (instruction.StackImpact < 0) // 负值表示堆栈增长
            {
                function.MaxStackUsage = Math.Max(function.MaxStackUsage, -function.TotalStackImpact);
            }
            
            // 检查特殊指令
            switch (instruction.InstructionType)
            {
                case InstructionType.FunctionCall:
                    AnalyzeFunctionCall(instruction, function);
                    break;
                    
                case InstructionType.Return:
                    function.HasReturn = true;
                    break;
                    
                case InstructionType.StackOperation:
                    function.StackOperations.Add(instruction);
                    break;
            }
        }

        /// <summary>
        /// 分析函数调用
        /// </summary>
        private void AnalyzeFunctionCall(AssemblyInstruction instruction, AssemblyFunction function)
        {
            // 提取被调用的函数名
            string calledFunction = ExtractCalledFunctionName(instruction.Operands);
            if (!string.IsNullOrEmpty(calledFunction))
            {
                function.CalledFunctions.Add(calledFunction);
            }
        }

        /// <summary>
        /// 提取被调用的函数名
        /// </summary>
        private string ExtractCalledFunctionName(string operands)
        {
            // 简化的函数名提取
            var match = Regex.Match(operands, @"([a-zA-Z_][a-zA-Z0-9_]*)");
            return match.Success ? match.Groups[1].Value : null;
        }

        /// <summary>
        /// 计算指令的堆栈影响
        /// </summary>
        private int CalculateInstructionStackImpact(string mnemonic, string operands)
        {
            switch (mnemonic)
            {
                case "st.w":
                    return operands.Contains("sp") ? -4 : 0;
                case "st.h":
                    return operands.Contains("sp") ? -2 : 0;
                case "st.b":
                    return operands.Contains("sp") ? -1 : 0;
                    
                case "ld.w":
                    return operands.Contains("sp") ? 4 : 0;
                case "ld.h":
                    return operands.Contains("sp") ? 2 : 0;
                case "ld.b":
                    return operands.Contains("sp") ? 1 : 0;
                    
                case "add":
                    return AnalyzeAddInstruction(operands);
                    
                case "pushsp":
                    return AnalyzePushSpInstruction(operands);
                case "popsp":
                    return AnalyzePopSpInstruction(operands);
                    
                case "jarl":
                case "jr":
                    return -4; // 函数调用，保存返回地址
                    
                default:
                    return 0;
            }
        }

        /// <summary>
        /// 分析ADD指令
        /// </summary>
        private int AnalyzeAddInstruction(string operands)
        {
            // 解析 "add immediate, sp" 形式的指令
            var parts = operands.Split(',');
            if (parts.Length == 2 && parts[1].Trim() == "sp")
            {
                if (int.TryParse(parts[0].Trim(), out int immediate))
                {
                    return immediate;
                }
            }
            return 0;
        }

        /// <summary>
        /// 分析PUSHSP指令
        /// </summary>
        private int AnalyzePushSpInstruction(string operands)
        {
            // PUSHSP指令压入寄存器范围
            var match = Regex.Match(operands, @"r(\d+)-r(\d+)");
            if (match.Success)
            {
                int startReg = int.Parse(match.Groups[1].Value);
                int endReg = int.Parse(match.Groups[2].Value);
                int regCount = endReg - startReg + 1;
                return -regCount * 4; // 每个寄存器4字节
            }
            
            // 单个寄存器
            if (operands.StartsWith("r"))
            {
                return -4;
            }
            
            return 0;
        }

        /// <summary>
        /// 分析POPSP指令
        /// </summary>
        private int AnalyzePopSpInstruction(string operands)
        {
            // POPSP指令弹出寄存器范围
            var match = Regex.Match(operands, @"r(\d+)-r(\d+)");
            if (match.Success)
            {
                int startReg = int.Parse(match.Groups[1].Value);
                int endReg = int.Parse(match.Groups[2].Value);
                int regCount = endReg - startReg + 1;
                return regCount * 4; // 每个寄存器4字节
            }
            
            // 单个寄存器
            if (operands.StartsWith("r"))
            {
                return 4;
            }
            
            return 0;
        }

        /// <summary>
        /// 初始化指令集
        /// </summary>
        private Dictionary<string, RH850Instruction> InitializeInstructionSet()
        {
            return new Dictionary<string, RH850Instruction>
            {
                // 数据传输指令
                ["mov"] = new RH850Instruction { Type = InstructionType.DataTransfer },
                ["ld.b"] = new RH850Instruction { Type = InstructionType.Load },
                ["ld.h"] = new RH850Instruction { Type = InstructionType.Load },
                ["ld.w"] = new RH850Instruction { Type = InstructionType.Load },
                ["st.b"] = new RH850Instruction { Type = InstructionType.Store },
                ["st.h"] = new RH850Instruction { Type = InstructionType.Store },
                ["st.w"] = new RH850Instruction { Type = InstructionType.Store },
                
                // 算术指令
                ["add"] = new RH850Instruction { Type = InstructionType.Arithmetic },
                ["sub"] = new RH850Instruction { Type = InstructionType.Arithmetic },
                ["mul"] = new RH850Instruction { Type = InstructionType.Arithmetic },
                ["div"] = new RH850Instruction { Type = InstructionType.Arithmetic },
                
                // 逻辑指令
                ["and"] = new RH850Instruction { Type = InstructionType.Logic },
                ["or"] = new RH850Instruction { Type = InstructionType.Logic },
                ["xor"] = new RH850Instruction { Type = InstructionType.Logic },
                ["not"] = new RH850Instruction { Type = InstructionType.Logic },
                
                // 分支指令
                ["br"] = new RH850Instruction { Type = InstructionType.Branch },
                ["bz"] = new RH850Instruction { Type = InstructionType.Branch },
                ["bnz"] = new RH850Instruction { Type = InstructionType.Branch },
                ["bc"] = new RH850Instruction { Type = InstructionType.Branch },
                ["bnc"] = new RH850Instruction { Type = InstructionType.Branch },
                
                // 函数调用指令
                ["jarl"] = new RH850Instruction { Type = InstructionType.FunctionCall },
                ["jr"] = new RH850Instruction { Type = InstructionType.FunctionCall },
                ["jmp"] = new RH850Instruction { Type = InstructionType.Jump },
                
                // 返回指令
                ["jmp"] = new RH850Instruction { Type = InstructionType.Return },
                
                // 堆栈操作指令
                ["pushsp"] = new RH850Instruction { Type = InstructionType.StackOperation },
                ["popsp"] = new RH850Instruction { Type = InstructionType.StackOperation },
                
                // 系统指令
                ["halt"] = new RH850Instruction { Type = InstructionType.System },
                ["nop"] = new RH850Instruction { Type = InstructionType.System },
                ["reti"] = new RH850Instruction { Type = InstructionType.Return }
            };
        }
    }

    /// <summary>
    /// 汇编函数信息
    /// </summary>
    public class AssemblyFunction
    {
        public string Name { get; set; }
        public string SourceFile { get; set; }
        public int StartLine { get; set; }
        public List<AssemblyInstruction> Instructions { get; set; }
        public List<string> CalledFunctions { get; set; }
        public List<AssemblyInstruction> StackOperations { get; set; }
        public int TotalStackImpact { get; set; }
        public int MaxStackUsage { get; set; }
        public bool HasReturn { get; set; }

        public AssemblyFunction()
        {
            Instructions = new List<AssemblyInstruction>();
            CalledFunctions = new List<string>();
            StackOperations = new List<AssemblyInstruction>();
        }
    }

    /// <summary>
    /// 汇编指令信息
    /// </summary>
    public class AssemblyInstruction
    {
        public string Mnemonic { get; set; }
        public string Operands { get; set; }
        public int LineNumber { get; set; }
        public string OriginalText { get; set; }
        public InstructionType InstructionType { get; set; }
        public int StackImpact { get; set; }
    }

    /// <summary>
    /// RH850指令信息
    /// </summary>
    public class RH850Instruction
    {
        public InstructionType Type { get; set; }
        public int CycleCount { get; set; }
        public string Description { get; set; }
    }

    /// <summary>
    /// 指令类型枚举
    /// </summary>
    public enum InstructionType
    {
        DataTransfer,
        Load,
        Store,
        Arithmetic,
        Logic,
        Branch,
        Jump,
        FunctionCall,
        Return,
        StackOperation,
        System
    }
}
