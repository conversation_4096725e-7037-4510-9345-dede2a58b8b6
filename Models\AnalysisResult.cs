using System;
using System.Collections.Generic;
using System.Linq;

namespace CalculateStackSize.Models
{
    /// <summary>
    /// 堆栈分析结果
    /// </summary>
    public class AnalysisResult
    {
        /// <summary>
        /// 分析的项目路径
        /// </summary>
        public string ProjectPath { get; set; }
        
        /// <summary>
        /// 分析时间
        /// </summary>
        public DateTime AnalysisTime { get; set; }
        
        /// <summary>
        /// 目标处理器架构
        /// </summary>
        public ProcessorArchitecture Architecture { get; set; }
        
        /// <summary>
        /// 调用图
        /// </summary>
        public CallGraph CallGraph { get; set; }
        
        /// <summary>
        /// 任务堆栈分析结果
        /// </summary>
        public List<TaskStackAnalysis> TaskAnalyses { get; set; }
        
        /// <summary>
        /// 中断堆栈分析结果
        /// </summary>
        public List<InterruptStackAnalysis> InterruptAnalyses { get; set; }
        
        /// <summary>
        /// 堆栈使用警告
        /// </summary>
        public List<StackWarning> Warnings { get; set; }
        
        /// <summary>
        /// 分析统计信息
        /// </summary>
        public AnalysisStatistics Statistics { get; set; }

        public AnalysisResult()
        {
            AnalysisTime = DateTime.Now;
            TaskAnalyses = new List<TaskStackAnalysis>();
            InterruptAnalyses = new List<InterruptStackAnalysis>();
            Warnings = new List<StackWarning>();
        }

        /// <summary>
        /// 获取最大堆栈使用量
        /// </summary>
        public int GetMaxStackUsage()
        {
            int maxTaskStack = TaskAnalyses.Any() ? TaskAnalyses.Max(t => t.MaxStackUsage) : 0;
            int maxInterruptStack = InterruptAnalyses.Any() ? InterruptAnalyses.Max(i => i.MaxStackUsage) : 0;
            return Math.Max(maxTaskStack, maxInterruptStack);
        }

        /// <summary>
        /// 获取总的堆栈需求（考虑中断嵌套）
        /// </summary>
        public int GetTotalStackRequirement()
        {
            // 计算最坏情况下的堆栈需求
            int maxTaskStack = GetMaxStackUsage();
            int maxInterruptNesting = CalculateMaxInterruptNesting();
            
            return maxTaskStack + maxInterruptNesting;
        }

        /// <summary>
        /// 计算最大中断嵌套堆栈
        /// </summary>
        private int CalculateMaxInterruptNesting()
        {
            if (!InterruptAnalyses.Any()) return 0;
            
            // 按优先级排序中断
            var sortedInterrupts = InterruptAnalyses
                .OrderBy(i => i.Priority)
                .ToList();
            
            int totalNestingStack = 0;
            
            // 计算可能的中断嵌套堆栈
            foreach (var interrupt in sortedInterrupts)
            {
                totalNestingStack += interrupt.MaxStackUsage;
            }
            
            return totalNestingStack;
        }
    }

    /// <summary>
    /// 任务堆栈分析结果
    /// </summary>
    public class TaskStackAnalysis
    {
        /// <summary>
        /// 任务名称
        /// </summary>
        public string TaskName { get; set; }
        
        /// <summary>
        /// 任务ID
        /// </summary>
        public string TaskId { get; set; }
        
        /// <summary>
        /// 任务入口函数
        /// </summary>
        public Function EntryFunction { get; set; }
        
        /// <summary>
        /// 最大堆栈使用量
        /// </summary>
        public int MaxStackUsage { get; set; }
        
        /// <summary>
        /// 配置的堆栈大小
        /// </summary>
        public int ConfiguredStackSize { get; set; }
        
        /// <summary>
        /// 最深调用路径
        /// </summary>
        public CallPath DeepestCallPath { get; set; }
        
        /// <summary>
        /// 所有可能的调用路径
        /// </summary>
        public List<CallPath> AllCallPaths { get; set; }
        
        /// <summary>
        /// 任务优先级
        /// </summary>
        public int Priority { get; set; }
        
        /// <summary>
        /// 是否存在堆栈溢出风险
        /// </summary>
        public bool HasOverflowRisk => MaxStackUsage > ConfiguredStackSize;
        
        /// <summary>
        /// 堆栈利用率
        /// </summary>
        public double StackUtilization => ConfiguredStackSize > 0 ? 
            (double)MaxStackUsage / ConfiguredStackSize : 0;

        public TaskStackAnalysis()
        {
            AllCallPaths = new List<CallPath>();
        }
    }

    /// <summary>
    /// 中断堆栈分析结果
    /// </summary>
    public class InterruptStackAnalysis
    {
        /// <summary>
        /// 中断名称
        /// </summary>
        public string InterruptName { get; set; }
        
        /// <summary>
        /// 中断向量号
        /// </summary>
        public int VectorNumber { get; set; }
        
        /// <summary>
        /// 中断优先级
        /// </summary>
        public int Priority { get; set; }
        
        /// <summary>
        /// 中断服务例程函数
        /// </summary>
        public Function HandlerFunction { get; set; }
        
        /// <summary>
        /// 最大堆栈使用量
        /// </summary>
        public int MaxStackUsage { get; set; }
        
        /// <summary>
        /// 中断上下文保存大小
        /// </summary>
        public int ContextSaveSize { get; set; }
        
        /// <summary>
        /// 最深调用路径
        /// </summary>
        public CallPath DeepestCallPath { get; set; }
        
        /// <summary>
        /// 是否可以被其他中断打断
        /// </summary>
        public bool CanBePreempted { get; set; }
        
        /// <summary>
        /// 可能打断此中断的其他中断
        /// </summary>
        public List<InterruptStackAnalysis> PreemptingInterrupts { get; set; }

        public InterruptStackAnalysis()
        {
            PreemptingInterrupts = new List<InterruptStackAnalysis>();
        }
    }

    /// <summary>
    /// 堆栈警告
    /// </summary>
    public class StackWarning
    {
        /// <summary>
        /// 警告类型
        /// </summary>
        public WarningType Type { get; set; }
        
        /// <summary>
        /// 警告级别
        /// </summary>
        public WarningLevel Level { get; set; }
        
        /// <summary>
        /// 警告消息
        /// </summary>
        public string Message { get; set; }
        
        /// <summary>
        /// 相关函数
        /// </summary>
        public Function RelatedFunction { get; set; }
        
        /// <summary>
        /// 相关任务或中断
        /// </summary>
        public string RelatedContext { get; set; }
        
        /// <summary>
        /// 建议的解决方案
        /// </summary>
        public string Suggestion { get; set; }
        
        /// <summary>
        /// 源文件位置
        /// </summary>
        public string SourceLocation { get; set; }
    }

    /// <summary>
    /// 警告类型
    /// </summary>
    public enum WarningType
    {
        StackOverflow,        // 堆栈溢出
        RecursiveCall,        // 递归调用
        LargeLocalVariable,   // 大型局部变量
        DeepCallChain,        // 深度调用链
        UnknownFunctionCall,  // 未知函数调用
        InterruptNesting,     // 中断嵌套
        InsufficientStack     // 堆栈配置不足
    }

    /// <summary>
    /// 警告级别
    /// </summary>
    public enum WarningLevel
    {
        Info,     // 信息
        Warning,  // 警告
        Error,    // 错误
        Critical  // 严重错误
    }

    /// <summary>
    /// 分析统计信息
    /// </summary>
    public class AnalysisStatistics
    {
        /// <summary>
        /// 分析的源文件数量
        /// </summary>
        public int SourceFileCount { get; set; }
        
        /// <summary>
        /// 分析的函数数量
        /// </summary>
        public int FunctionCount { get; set; }
        
        /// <summary>
        /// 任务数量
        /// </summary>
        public int TaskCount { get; set; }
        
        /// <summary>
        /// 中断数量
        /// </summary>
        public int InterruptCount { get; set; }
        
        /// <summary>
        /// 递归函数数量
        /// </summary>
        public int RecursiveFunctionCount { get; set; }
        
        /// <summary>
        /// 最大调用深度
        /// </summary>
        public int MaxCallDepth { get; set; }
        
        /// <summary>
        /// 平均函数堆栈大小
        /// </summary>
        public double AverageStackSize { get; set; }
        
        /// <summary>
        /// 分析耗时（毫秒）
        /// </summary>
        public long AnalysisTimeMs { get; set; }
        
        /// <summary>
        /// 警告数量统计
        /// </summary>
        public Dictionary<WarningLevel, int> WarningCounts { get; set; }

        public AnalysisStatistics()
        {
            WarningCounts = new Dictionary<WarningLevel, int>();
        }
    }
}
