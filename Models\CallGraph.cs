using System;
using System.Collections.Generic;
using System.Linq;

namespace CalculateStackSize.Models
{
    /// <summary>
    /// 函数调用图
    /// </summary>
    public class CallGraph
    {
        /// <summary>
        /// 所有函数的集合
        /// </summary>
        public Dictionary<string, Function> Functions { get; set; }
        
        /// <summary>
        /// 入口点函数（任务函数、中断服务例程等）
        /// </summary>
        public List<Function> EntryPoints { get; set; }
        
        /// <summary>
        /// 递归调用组
        /// </summary>
        public List<RecursionGroup> RecursionGroups { get; set; }
        
        /// <summary>
        /// 孤立函数（没有被调用的函数）
        /// </summary>
        public List<Function> OrphanFunctions { get; set; }

        public CallGraph()
        {
            Functions = new Dictionary<string, Function>();
            EntryPoints = new List<Function>();
            RecursionGroups = new List<RecursionGroup>();
            OrphanFunctions = new List<Function>();
        }

        /// <summary>
        /// 添加函数到调用图
        /// </summary>
        public void AddFunction(Function function)
        {
            string key = GetFunctionKey(function);
            if (!Functions.ContainsKey(key))
            {
                Functions[key] = function;
            }
        }

        /// <summary>
        /// 获取函数的唯一键
        /// </summary>
        private string GetFunctionKey(Function function)
        {
            return $"{function.SourceFile}:{function.Name}";
        }

        /// <summary>
        /// 查找函数
        /// </summary>
        public Function FindFunction(string name, string sourceFile = null)
        {
            if (sourceFile != null)
            {
                string key = $"{sourceFile}:{name}";
                return Functions.ContainsKey(key) ? Functions[key] : null;
            }
            
            // 如果没有指定源文件，查找第一个匹配的函数
            return Functions.Values.FirstOrDefault(f => f.Name == name);
        }

        /// <summary>
        /// 添加入口点
        /// </summary>
        public void AddEntryPoint(Function function)
        {
            if (!EntryPoints.Contains(function))
            {
                EntryPoints.Add(function);
            }
        }

        /// <summary>
        /// 检测递归调用
        /// </summary>
        public void DetectRecursion()
        {
            RecursionGroups.Clear();
            var visited = new HashSet<Function>();
            var recursionStack = new HashSet<Function>();

            foreach (var function in Functions.Values)
            {
                if (!visited.Contains(function))
                {
                    DetectRecursionDFS(function, visited, recursionStack, new List<Function>());
                }
            }
        }

        private void DetectRecursionDFS(Function current, HashSet<Function> visited, 
            HashSet<Function> recursionStack, List<Function> path)
        {
            visited.Add(current);
            recursionStack.Add(current);
            path.Add(current);

            foreach (var call in current.CalledFunctions)
            {
                var calledFunction = call.Function;
                
                if (recursionStack.Contains(calledFunction))
                {
                    // 发现递归调用
                    var recursionStart = path.IndexOf(calledFunction);
                    var recursionPath = path.Skip(recursionStart).ToList();
                    
                    var group = new RecursionGroup
                    {
                        Functions = new List<Function>(recursionPath),
                        RecursionType = recursionPath.Count == 1 ? RecursionType.Direct : RecursionType.Indirect
                    };
                    
                    // 标记递归函数
                    foreach (var func in recursionPath)
                    {
                        func.IsRecursive = true;
                    }
                    
                    RecursionGroups.Add(group);
                }
                else if (!visited.Contains(calledFunction))
                {
                    DetectRecursionDFS(calledFunction, visited, recursionStack, path);
                }
            }

            recursionStack.Remove(current);
            path.RemoveAt(path.Count - 1);
        }

        /// <summary>
        /// 查找孤立函数
        /// </summary>
        public void FindOrphanFunctions()
        {
            OrphanFunctions.Clear();
            
            foreach (var function in Functions.Values)
            {
                if (function.CallerFunctions.Count == 0 && 
                    function.Type == FunctionType.Normal)
                {
                    OrphanFunctions.Add(function);
                }
            }
        }

        /// <summary>
        /// 获取从指定函数开始的所有调用路径
        /// </summary>
        public List<CallPath> GetAllCallPaths(Function startFunction, int maxDepth = 100)
        {
            var paths = new List<CallPath>();
            var visited = new HashSet<Function>();
            
            GetCallPathsDFS(startFunction, new CallPath(), paths, visited, maxDepth);
            
            return paths;
        }

        private void GetCallPathsDFS(Function current, CallPath currentPath, 
            List<CallPath> allPaths, HashSet<Function> visited, int remainingDepth)
        {
            if (remainingDepth <= 0 || visited.Contains(current))
            {
                return;
            }

            visited.Add(current);
            currentPath.Functions.Add(current);

            if (current.CalledFunctions.Count == 0)
            {
                // 叶子节点，添加路径
                allPaths.Add(new CallPath(currentPath));
            }
            else
            {
                foreach (var call in current.CalledFunctions)
                {
                    GetCallPathsDFS(call.Function, currentPath, allPaths, visited, remainingDepth - 1);
                }
            }

            currentPath.Functions.RemoveAt(currentPath.Functions.Count - 1);
            visited.Remove(current);
        }

        /// <summary>
        /// 获取调用图统计信息
        /// </summary>
        public CallGraphStatistics GetStatistics()
        {
            return new CallGraphStatistics
            {
                TotalFunctions = Functions.Count,
                EntryPointCount = EntryPoints.Count,
                RecursionGroupCount = RecursionGroups.Count,
                OrphanFunctionCount = OrphanFunctions.Count,
                InterruptHandlerCount = Functions.Values.Count(f => f.Type == FunctionType.InterruptHandler),
                TaskFunctionCount = Functions.Values.Count(f => f.Type == FunctionType.TaskFunction)
            };
        }
    }

    /// <summary>
    /// 递归调用组
    /// </summary>
    public class RecursionGroup
    {
        public List<Function> Functions { get; set; }
        public RecursionType RecursionType { get; set; }
        public int EstimatedMaxDepth { get; set; }

        public RecursionGroup()
        {
            Functions = new List<Function>();
            EstimatedMaxDepth = 10; // 默认最大递归深度
        }
    }

    /// <summary>
    /// 递归类型
    /// </summary>
    public enum RecursionType
    {
        Direct,   // 直接递归（函数调用自己）
        Indirect  // 间接递归（A调用B，B调用A）
    }

    /// <summary>
    /// 调用路径
    /// </summary>
    public class CallPath
    {
        public List<Function> Functions { get; set; }
        public int TotalStackSize { get; set; }

        public CallPath()
        {
            Functions = new List<Function>();
        }

        public CallPath(CallPath other)
        {
            Functions = new List<Function>(other.Functions);
            TotalStackSize = other.TotalStackSize;
        }

        /// <summary>
        /// 计算路径的总堆栈大小
        /// </summary>
        public void CalculateTotalStackSize()
        {
            TotalStackSize = Functions.Sum(f => f.GetBaseStackSize());
        }

        public override string ToString()
        {
            return string.Join(" -> ", Functions.Select(f => f.Name)) + $" (Total: {TotalStackSize} bytes)";
        }
    }

    /// <summary>
    /// 调用图统计信息
    /// </summary>
    public class CallGraphStatistics
    {
        public int TotalFunctions { get; set; }
        public int EntryPointCount { get; set; }
        public int RecursionGroupCount { get; set; }
        public int OrphanFunctionCount { get; set; }
        public int InterruptHandlerCount { get; set; }
        public int TaskFunctionCount { get; set; }
    }
}
