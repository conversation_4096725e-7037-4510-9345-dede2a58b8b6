/* AUTOSAR OS Configuration in OIL Format */

CPU ExampleCPU {
    OS ExampleOS {
        STATUS = EXTENDED;
        STARTUPHOOK = TRUE;
        SHUTDOWNHOOK = TRUE;
        ERRORHOOK = TRUE;
        PRETASKHOOK = FALSE;
        POSTTASKHOOK = FALSE;
        USEGETSERVICEID = FALSE;
        USEPARAMETERACCESS = FALSE;
        USERESSCHEDULER = FALSE;
        STACKSIZE = 4096;
    };

    /* Task Definitions */
    TASK TaskMain {
        PRIORITY = 10;
        ACTIVATION = 1;
        SCHEDULE = FULL;
        AUTOSTART = TRUE;
        STACKSIZE = 2048;
        EVENT = EventMain;
    };

    TASK TaskPeriodic {
        PRIORITY = 5;
        ACTIVATION = 1;
        SCHEDULE = FULL;
        AUTOSTART = FALSE;
        STACKSIZE = 1024;
        EVENT = EventPeriodic;
    };

    TASK TaskBackground {
        PRIORITY = 1;
        ACTIVATION = 1;
        SCHEDULE = FULL;
        AUTOSTART = TRUE;
        STACKSIZE = 512;
    };

    /* ISR Definitions */
    ISR TimerISR {
        CATEGORY = 2;
        PRIORITY = 15;
        VECTOR = 10;
    };

    ISR CanISR {
        CATEGORY = 2;
        PRIORITY = 12;
        VECTOR = 20;
    };

    ISR AdcISR {
        CATEGORY = 2;
        PRIORITY = 8;
        VECTOR = 30;
    };

    /* Event Definitions */
    EVENT EventMain {
        MASK = AUTO;
    };

    EVENT EventPeriodic {
        MASK = AUTO;
    };

    /* Alarm Definitions */
    ALARM AlarmPeriodic {
        COUNTER = SystemCounter;
        ACTION = ACTIVATETASK {
            TASK = TaskPeriodic;
        };
        AUTOSTART = TRUE {
            ALARMTIME = 100;
            CYCLETIME = 100;
        };
    };

    /* Counter Definitions */
    COUNTER SystemCounter {
        MINCYCLE = 1;
        MAXALLOWEDVALUE = 65535;
        TICKSPERBASE = 1;
    };

    /* Resource Definitions */
    RESOURCE ResourceShared {
        RESOURCEPROPERTY = STANDARD;
    };
};
