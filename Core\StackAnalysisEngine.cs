using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using CalculateStackSize.Models;
using CalculateStackSize.Core.Analysis;
using CalculateStackSize.Core.Parser;
using CalculateStackSize.Processors.RH850;
using CalculateStackSize.Processors.TC377;

namespace CalculateStackSize.Core
{
    /// <summary>
    /// 堆栈分析引擎 - 整合所有分析组件的主要类
    /// </summary>
    public class StackAnalysisEngine
    {
        private readonly CallGraphAnalyzer _callGraphAnalyzer;
        private readonly RecursionDetector _recursionDetector;
        private readonly StackCalculator _stackCalculator;
        private readonly IProcessorSpecific _processor;

        public StackAnalysisEngine(ProcessorArchitecture architecture)
        {
            _callGraphAnalyzer = new CallGraphAnalyzer();
            _recursionDetector = new RecursionDetector();
            
            // 根据架构选择处理器
            _processor = CreateProcessor(architecture);
            _stackCalculator = new StackCalculator(architecture);
        }

        /// <summary>
        /// 执行完整的堆栈分析
        /// </summary>
        public AnalysisResult AnalyzeProject(AnalysisConfiguration config)
        {
            var result = new AnalysisResult
            {
                ProjectPath = config.ProjectPath,
                Architecture = _processor.Architecture,
                AnalysisTime = DateTime.Now
            };

            var startTime = DateTime.Now;

            try
            {
                // 1. 构建调用图
                Console.WriteLine("Building call graph...");
                result.CallGraph = _callGraphAnalyzer.AnalyzeProject(config.ProjectPath, config.AutosarConfigPath);

                // 2. 检测递归调用
                Console.WriteLine("Detecting recursion...");
                var recursionResult = _recursionDetector.DetectRecursion(result.CallGraph);
                ProcessRecursionResults(recursionResult, result);

                // 3. 计算堆栈使用
                Console.WriteLine("Calculating stack usage...");
                var stackResult = _stackCalculator.CalculateStackUsage(result.CallGraph);
                ProcessStackResults(stackResult, result);

                // 4. 分析汇编代码（如果提供）
                if (!string.IsNullOrEmpty(config.AssemblyPath))
                {
                    Console.WriteLine("Analyzing assembly code...");
                    AnalyzeAssemblyCode(config.AssemblyPath, result);
                }

                // 5. 生成统计信息
                result.Statistics = GenerateStatistics(result, startTime);

                Console.WriteLine($"Analysis completed successfully in {result.Statistics.AnalysisTimeMs}ms");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Analysis failed: {ex.Message}");
                result.Warnings.Add(new StackWarning
                {
                    Type = WarningType.UnknownFunctionCall,
                    Level = WarningLevel.Critical,
                    Message = $"Analysis failed: {ex.Message}",
                    Suggestion = "Please check the project configuration and try again."
                });
            }

            return result;
        }

        /// <summary>
        /// 创建处理器实例
        /// </summary>
        private IProcessorSpecific CreateProcessor(ProcessorArchitecture architecture)
        {
            switch (architecture)
            {
                case ProcessorArchitecture.RH850:
                    return new RH850Processor();
                case ProcessorArchitecture.TC377:
                    return new TC377Processor();
                default:
                    throw new NotSupportedException($"Processor architecture {architecture} is not supported");
            }
        }

        /// <summary>
        /// 处理递归检测结果
        /// </summary>
        private void ProcessRecursionResults(RecursionAnalysisResult recursionResult, AnalysisResult result)
        {
            foreach (var group in recursionResult.RecursionGroups)
            {
                result.Warnings.Add(new StackWarning
                {
                    Type = WarningType.RecursiveCall,
                    Level = MapRecursionRiskToWarningLevel(group.RiskLevel),
                    Message = $"Detected {group.RecursionType} recursion in functions: {string.Join(", ", group.Functions.Select(f => f.Name))}",
                    RelatedFunction = group.Functions.FirstOrDefault(),
                    Suggestion = string.Join("; ", group.Recommendations)
                });
            }
        }

        /// <summary>
        /// 处理堆栈计算结果
        /// </summary>
        private void ProcessStackResults(StackCalculationResult stackResult, AnalysisResult result)
        {
            result.TaskAnalyses = stackResult.TaskAnalyses;
            result.InterruptAnalyses = stackResult.InterruptAnalyses;
            result.Warnings.AddRange(stackResult.Warnings);
        }

        /// <summary>
        /// 分析汇编代码
        /// </summary>
        private void AnalyzeAssemblyCode(string assemblyPath, AnalysisResult result)
        {
            try
            {
                List<AssemblyFunction> assemblyFunctions = null;

                switch (_processor.Architecture)
                {
                    case ProcessorArchitecture.RH850:
                        var rh850Parser = new RH850AssemblyParser();
                        assemblyFunctions = rh850Parser.ParseAssemblyFile(assemblyPath);
                        break;
                    case ProcessorArchitecture.TC377:
                        var tc377Parser = new TC377AssemblyParser();
                        assemblyFunctions = tc377Parser.ParseAssemblyFile(assemblyPath);
                        break;
                }

                if (assemblyFunctions != null)
                {
                    // 将汇编分析结果与C代码分析结果合并
                    MergeAssemblyResults(assemblyFunctions, result);
                }
            }
            catch (Exception ex)
            {
                result.Warnings.Add(new StackWarning
                {
                    Type = WarningType.UnknownFunctionCall,
                    Level = WarningLevel.Warning,
                    Message = $"Failed to analyze assembly code: {ex.Message}",
                    Suggestion = "Assembly analysis is optional. The main analysis results are still valid."
                });
            }
        }

        /// <summary>
        /// 合并汇编分析结果
        /// </summary>
        private void MergeAssemblyResults(List<AssemblyFunction> assemblyFunctions, AnalysisResult result)
        {
            foreach (var asmFunc in assemblyFunctions)
            {
                var cFunction = result.CallGraph.FindFunction(asmFunc.Name);
                if (cFunction != null)
                {
                    // 更新堆栈使用信息
                    if (asmFunc.MaxStackUsage > cFunction.GetBaseStackSize())
                    {
                        result.Warnings.Add(new StackWarning
                        {
                            Type = WarningType.LargeLocalVariable,
                            Level = WarningLevel.Info,
                            Message = $"Assembly analysis shows higher stack usage for function '{asmFunc.Name}': {asmFunc.MaxStackUsage} bytes",
                            RelatedFunction = cFunction,
                            Suggestion = "Consider the assembly-level stack usage in your analysis."
                        });
                    }
                }
                else
                {
                    // 发现汇编中的函数但C代码中没有
                    result.Warnings.Add(new StackWarning
                    {
                        Type = WarningType.UnknownFunctionCall,
                        Level = WarningLevel.Info,
                        Message = $"Function '{asmFunc.Name}' found in assembly but not in C source code",
                        Suggestion = "This might be an assembly-only function or inline assembly."
                    });
                }
            }
        }

        /// <summary>
        /// 生成统计信息
        /// </summary>
        private AnalysisStatistics GenerateStatistics(AnalysisResult result, DateTime startTime)
        {
            var endTime = DateTime.Now;
            var statistics = new AnalysisStatistics
            {
                FunctionCount = result.CallGraph.Functions.Count,
                TaskCount = result.TaskAnalyses.Count,
                InterruptCount = result.InterruptAnalyses.Count,
                RecursiveFunctionCount = result.CallGraph.Functions.Values.Count(f => f.IsRecursive),
                MaxCallDepth = CalculateMaxCallDepth(result),
                AverageStackSize = CalculateAverageStackSize(result),
                AnalysisTimeMs = (long)(endTime - startTime).TotalMilliseconds,
                WarningCounts = result.Warnings.GroupBy(w => w.Level).ToDictionary(g => g.Key, g => g.Count())
            };

            return statistics;
        }

        /// <summary>
        /// 计算最大调用深度
        /// </summary>
        private int CalculateMaxCallDepth(AnalysisResult result)
        {
            int maxDepth = 0;
            
            foreach (var taskAnalysis in result.TaskAnalyses)
            {
                if (taskAnalysis.DeepestCallPath != null)
                {
                    maxDepth = Math.Max(maxDepth, taskAnalysis.DeepestCallPath.Functions.Count);
                }
            }
            
            foreach (var interruptAnalysis in result.InterruptAnalyses)
            {
                if (interruptAnalysis.DeepestCallPath != null)
                {
                    maxDepth = Math.Max(maxDepth, interruptAnalysis.DeepestCallPath.Functions.Count);
                }
            }
            
            return maxDepth;
        }

        /// <summary>
        /// 计算平均堆栈大小
        /// </summary>
        private double CalculateAverageStackSize(AnalysisResult result)
        {
            var allStackSizes = new List<int>();
            
            allStackSizes.AddRange(result.TaskAnalyses.Select(t => t.MaxStackUsage));
            allStackSizes.AddRange(result.InterruptAnalyses.Select(i => i.MaxStackUsage));
            
            return allStackSizes.Any() ? allStackSizes.Average() : 0;
        }

        /// <summary>
        /// 映射递归风险级别到警告级别
        /// </summary>
        private WarningLevel MapRecursionRiskToWarningLevel(RecursionRiskLevel riskLevel)
        {
            switch (riskLevel)
            {
                case RecursionRiskLevel.Low:
                    return WarningLevel.Info;
                case RecursionRiskLevel.Medium:
                    return WarningLevel.Warning;
                case RecursionRiskLevel.High:
                    return WarningLevel.Error;
                case RecursionRiskLevel.Critical:
                    return WarningLevel.Critical;
                default:
                    return WarningLevel.Warning;
            }
        }

        /// <summary>
        /// 获取支持的处理器架构列表
        /// </summary>
        public static List<ProcessorArchitecture> GetSupportedArchitectures()
        {
            return new List<ProcessorArchitecture>
            {
                ProcessorArchitecture.RH850,
                ProcessorArchitecture.TC377
            };
        }

        /// <summary>
        /// 验证分析配置
        /// </summary>
        public static bool ValidateConfiguration(AnalysisConfiguration config, out string errorMessage)
        {
            errorMessage = null;

            if (string.IsNullOrEmpty(config.ProjectPath))
            {
                errorMessage = "Project path is required";
                return false;
            }

            if (!Directory.Exists(config.ProjectPath) && !File.Exists(config.ProjectPath))
            {
                errorMessage = "Project path does not exist";
                return false;
            }

            if (!string.IsNullOrEmpty(config.AutosarConfigPath) && !File.Exists(config.AutosarConfigPath))
            {
                errorMessage = "AUTOSAR configuration file does not exist";
                return false;
            }

            if (!string.IsNullOrEmpty(config.AssemblyPath) && !File.Exists(config.AssemblyPath))
            {
                errorMessage = "Assembly file does not exist";
                return false;
            }

            return true;
        }
    }

    /// <summary>
    /// 分析配置
    /// </summary>
    public class AnalysisConfiguration
    {
        public string ProjectPath { get; set; }
        public string AutosarConfigPath { get; set; }
        public string AssemblyPath { get; set; }
        public ProcessorArchitecture Architecture { get; set; }
        public bool IncludeAssemblyAnalysis { get; set; }
        public int MaxRecursionDepth { get; set; } = 10;
        public bool EnableDetailedWarnings { get; set; } = true;

        public AnalysisConfiguration()
        {
            Architecture = ProcessorArchitecture.RH850; // 默认架构
        }
    }
}
