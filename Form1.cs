﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using CalculateStackSize.Core;
using CalculateStackSize.Models;
using CalculateStackSize.Reports;

namespace CalculateStackSize
{
    public partial class Form1 : Form
    {
        private StackAnalysisEngine _analysisEngine;
        private AnalysisResult _lastResult;

        public Form1()
        {
            InitializeComponent();
            InitializeUI();
        }

        private void InitializeUI()
        {
            this.Text = "AUTOSAR Stack Size Calculator";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterScreen;

            CreateControls();
            SetupEventHandlers();
        }

        private void CreateControls()
        {
            // 主面板
            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 1
            };
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30F));
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70F));

            // 左侧配置面板
            var configPanel = CreateConfigurationPanel();
            mainPanel.Controls.Add(configPanel, 0, 0);

            // 右侧结果面板
            var resultPanel = CreateResultPanel();
            mainPanel.Controls.Add(resultPanel, 1, 0);

            this.Controls.Add(mainPanel);
        }

        private Panel CreateConfigurationPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10)
            };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                AutoSize = true
            };

            // 标题
            var titleLabel = new Label
            {
                Text = "Analysis Configuration",
                Font = new Font("Microsoft Sans Serif", 12F, FontStyle.Bold),
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 10)
            };
            layout.Controls.Add(titleLabel);

            // 项目路径
            layout.Controls.Add(new Label { Text = "Project Path:", AutoSize = true });
            var projectPathTextBox = new TextBox { Name = "projectPath", Width = 250, Anchor = AnchorStyles.Left | AnchorStyles.Right };
            var projectBrowseButton = new Button { Text = "Browse...", Name = "projectBrowse", Width = 80 };
            var projectPanel = new FlowLayoutPanel { FlowDirection = FlowDirection.LeftToRight, AutoSize = true };
            projectPanel.Controls.AddRange(new Control[] { projectPathTextBox, projectBrowseButton });
            layout.Controls.Add(projectPanel);

            // 处理器架构
            layout.Controls.Add(new Label { Text = "Processor Architecture:", AutoSize = true, Margin = new Padding(0, 10, 0, 0) });
            var archComboBox = new ComboBox
            {
                Name = "architecture",
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 200
            };
            archComboBox.Items.AddRange(new object[] { "RH850", "TC377" });
            archComboBox.SelectedIndex = 0;
            layout.Controls.Add(archComboBox);

            // AUTOSAR配置文件
            layout.Controls.Add(new Label { Text = "AUTOSAR Config (Optional):", AutoSize = true, Margin = new Padding(0, 10, 0, 0) });
            var autosarPathTextBox = new TextBox { Name = "autosarPath", Width = 250, Anchor = AnchorStyles.Left | AnchorStyles.Right };
            var autosarBrowseButton = new Button { Text = "Browse...", Name = "autosarBrowse", Width = 80 };
            var autosarPanel = new FlowLayoutPanel { FlowDirection = FlowDirection.LeftToRight, AutoSize = true };
            autosarPanel.Controls.AddRange(new Control[] { autosarPathTextBox, autosarBrowseButton });
            layout.Controls.Add(autosarPanel);

            // 汇编文件
            layout.Controls.Add(new Label { Text = "Assembly File (Optional):", AutoSize = true, Margin = new Padding(0, 10, 0, 0) });
            var assemblyPathTextBox = new TextBox { Name = "assemblyPath", Width = 250, Anchor = AnchorStyles.Left | AnchorStyles.Right };
            var assemblyBrowseButton = new Button { Text = "Browse...", Name = "assemblyBrowse", Width = 80 };
            var assemblyPanel = new FlowLayoutPanel { FlowDirection = FlowDirection.LeftToRight, AutoSize = true };
            assemblyPanel.Controls.AddRange(new Control[] { assemblyPathTextBox, assemblyBrowseButton });
            layout.Controls.Add(assemblyPanel);

            // 选项
            var optionsGroupBox = new GroupBox
            {
                Text = "Analysis Options",
                AutoSize = true,
                Margin = new Padding(0, 20, 0, 0),
                Anchor = AnchorStyles.Left | AnchorStyles.Right
            };
            var optionsLayout = new FlowLayoutPanel { FlowDirection = FlowDirection.TopDown, AutoSize = true, Dock = DockStyle.Fill };

            var includeAssemblyCheckBox = new CheckBox { Text = "Include Assembly Analysis", Name = "includeAssembly", AutoSize = true };
            var detailedWarningsCheckBox = new CheckBox { Text = "Enable Detailed Warnings", Name = "detailedWarnings", Checked = true, AutoSize = true };

            optionsLayout.Controls.AddRange(new Control[] { includeAssemblyCheckBox, detailedWarningsCheckBox });
            optionsGroupBox.Controls.Add(optionsLayout);
            layout.Controls.Add(optionsGroupBox);

            // 分析按钮
            var analyzeButton = new Button
            {
                Text = "Start Analysis",
                Name = "analyze",
                Size = new Size(120, 35),
                BackColor = Color.FromArgb(0, 120, 215),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Margin = new Padding(0, 20, 0, 0)
            };
            layout.Controls.Add(analyzeButton);

            // 报告生成按钮
            var generateReportButton = new Button
            {
                Text = "Generate Report",
                Name = "generateReport",
                Size = new Size(120, 35),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Enabled = false,
                Margin = new Padding(0, 10, 0, 0)
            };
            layout.Controls.Add(generateReportButton);

            // 进度条
            var progressBar = new ProgressBar
            {
                Name = "progress",
                Width = 250,
                Visible = false,
                Margin = new Padding(0, 10, 0, 0)
            };
            layout.Controls.Add(progressBar);

            panel.Controls.Add(layout);
            return panel;
        }

        private Panel CreateResultPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10)
            };

            var tabControl = new TabControl
            {
                Name = "resultTabs",
                Dock = DockStyle.Fill
            };

            // 概览标签页
            var overviewTab = new TabPage("Overview");
            var overviewTextBox = new RichTextBox
            {
                Name = "overviewText",
                Dock = DockStyle.Fill,
                ReadOnly = true,
                Font = new Font("Consolas", 9F)
            };
            overviewTab.Controls.Add(overviewTextBox);
            tabControl.TabPages.Add(overviewTab);

            // 任务分析标签页
            var tasksTab = new TabPage("Tasks");
            var tasksListView = new ListView
            {
                Name = "tasksList",
                Dock = DockStyle.Fill,
                View = View.Details,
                FullRowSelect = true,
                GridLines = true
            };
            tasksListView.Columns.AddRange(new ColumnHeader[]
            {
                new ColumnHeader { Text = "Task Name", Width = 150 },
                new ColumnHeader { Text = "Max Stack Usage", Width = 120 },
                new ColumnHeader { Text = "Configured Size", Width = 120 },
                new ColumnHeader { Text = "Utilization", Width = 100 },
                new ColumnHeader { Text = "Status", Width = 100 }
            });
            tasksTab.Controls.Add(tasksListView);
            tabControl.TabPages.Add(tasksTab);

            // 中断分析标签页
            var interruptsTab = new TabPage("Interrupts");
            var interruptsListView = new ListView
            {
                Name = "interruptsList",
                Dock = DockStyle.Fill,
                View = View.Details,
                FullRowSelect = true,
                GridLines = true
            };
            interruptsListView.Columns.AddRange(new ColumnHeader[]
            {
                new ColumnHeader { Text = "Interrupt Name", Width = 150 },
                new ColumnHeader { Text = "Priority", Width = 80 },
                new ColumnHeader { Text = "Max Stack Usage", Width = 120 },
                new ColumnHeader { Text = "Context Save", Width = 100 },
                new ColumnHeader { Text = "Handler Function", Width = 150 }
            });
            interruptsTab.Controls.Add(interruptsListView);
            tabControl.TabPages.Add(interruptsTab);

            // 警告标签页
            var warningsTab = new TabPage("Warnings");
            var warningsListView = new ListView
            {
                Name = "warningsList",
                Dock = DockStyle.Fill,
                View = View.Details,
                FullRowSelect = true,
                GridLines = true
            };
            warningsListView.Columns.AddRange(new ColumnHeader[]
            {
                new ColumnHeader { Text = "Level", Width = 80 },
                new ColumnHeader { Text = "Type", Width = 120 },
                new ColumnHeader { Text = "Message", Width = 300 },
                new ColumnHeader { Text = "Function", Width = 150 },
                new ColumnHeader { Text = "Suggestion", Width = 200 }
            });
            warningsTab.Controls.Add(warningsListView);
            tabControl.TabPages.Add(warningsTab);

            panel.Controls.Add(tabControl);
            return panel;
        }

        private void SetupEventHandlers()
        {
            // 浏览按钮事件
            var projectBrowseButton = this.Controls.Find("projectBrowse", true).FirstOrDefault() as Button;
            if (projectBrowseButton != null)
                projectBrowseButton.Click += ProjectBrowseButton_Click;

            var autosarBrowseButton = this.Controls.Find("autosarBrowse", true).FirstOrDefault() as Button;
            if (autosarBrowseButton != null)
                autosarBrowseButton.Click += AutosarBrowseButton_Click;

            var assemblyBrowseButton = this.Controls.Find("assemblyBrowse", true).FirstOrDefault() as Button;
            if (assemblyBrowseButton != null)
                assemblyBrowseButton.Click += AssemblyBrowseButton_Click;

            // 分析按钮事件
            var analyzeButton = this.Controls.Find("analyze", true).FirstOrDefault() as Button;
            if (analyzeButton != null)
                analyzeButton.Click += AnalyzeButton_Click;

            // 报告生成按钮事件
            var generateReportButton = this.Controls.Find("generateReport", true).FirstOrDefault() as Button;
            if (generateReportButton != null)
                generateReportButton.Click += GenerateReportButton_Click;
        }

        private void ProjectBrowseButton_Click(object sender, EventArgs e)
        {
            using (var dialog = new FolderBrowserDialog())
            {
                dialog.Description = "Select project folder containing C source files";
                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    var textBox = this.Controls.Find("projectPath", true).FirstOrDefault() as TextBox;
                    if (textBox != null)
                        textBox.Text = dialog.SelectedPath;
                }
            }
        }

        private void AutosarBrowseButton_Click(object sender, EventArgs e)
        {
            using (var dialog = new OpenFileDialog())
            {
                dialog.Filter = "AUTOSAR Config Files (*.xml;*.arxml;*.oil;*.c;*.h)|*.xml;*.arxml;*.oil;*.c;*.h|All Files (*.*)|*.*";
                dialog.Title = "Select AUTOSAR configuration file";
                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    var textBox = this.Controls.Find("autosarPath", true).FirstOrDefault() as TextBox;
                    if (textBox != null)
                        textBox.Text = dialog.FileName;
                }
            }
        }

        private void AssemblyBrowseButton_Click(object sender, EventArgs e)
        {
            using (var dialog = new OpenFileDialog())
            {
                dialog.Filter = "Assembly Files (*.s;*.asm)|*.s;*.asm|All Files (*.*)|*.*";
                dialog.Title = "Select assembly file";
                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    var textBox = this.Controls.Find("assemblyPath", true).FirstOrDefault() as TextBox;
                    if (textBox != null)
                        textBox.Text = dialog.FileName;
                }
            }
        }

        private async void AnalyzeButton_Click(object sender, EventArgs e)
        {
            var config = GetAnalysisConfiguration();
            if (config == null) return;

            var analyzeButton = sender as Button;
            var progressBar = this.Controls.Find("progress", true).FirstOrDefault() as ProgressBar;

            try
            {
                // 禁用按钮并显示进度条
                analyzeButton.Enabled = false;
                progressBar.Visible = true;
                progressBar.Style = ProgressBarStyle.Marquee;

                // 在后台线程执行分析
                _lastResult = await Task.Run(() =>
                {
                    _analysisEngine = new StackAnalysisEngine(config.Architecture);
                    return _analysisEngine.AnalyzeProject(config);
                });

                // 显示结果
                DisplayResults(_lastResult);

                // 启用报告生成按钮
                var generateReportButton = this.Controls.Find("generateReport", true).FirstOrDefault() as Button;
                if (generateReportButton != null)
                    generateReportButton.Enabled = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Analysis failed: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // 恢复UI状态
                analyzeButton.Enabled = true;
                progressBar.Visible = false;
            }
        }

        private AnalysisConfiguration GetAnalysisConfiguration()
        {
            var projectPathTextBox = this.Controls.Find("projectPath", true).FirstOrDefault() as TextBox;
            var archComboBox = this.Controls.Find("architecture", true).FirstOrDefault() as ComboBox;
            var autosarPathTextBox = this.Controls.Find("autosarPath", true).FirstOrDefault() as TextBox;
            var assemblyPathTextBox = this.Controls.Find("assemblyPath", true).FirstOrDefault() as TextBox;
            var includeAssemblyCheckBox = this.Controls.Find("includeAssembly", true).FirstOrDefault() as CheckBox;
            var detailedWarningsCheckBox = this.Controls.Find("detailedWarnings", true).FirstOrDefault() as CheckBox;

            if (string.IsNullOrWhiteSpace(projectPathTextBox?.Text))
            {
                MessageBox.Show("Please select a project path.", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return null;
            }

            var config = new AnalysisConfiguration
            {
                ProjectPath = projectPathTextBox.Text,
                AutosarConfigPath = autosarPathTextBox?.Text,
                AssemblyPath = assemblyPathTextBox?.Text,
                Architecture = archComboBox?.SelectedItem?.ToString() == "TC377" ? ProcessorArchitecture.TC377 : ProcessorArchitecture.RH850,
                IncludeAssemblyAnalysis = includeAssemblyCheckBox?.Checked ?? false,
                EnableDetailedWarnings = detailedWarningsCheckBox?.Checked ?? true
            };

            // 验证配置
            if (!StackAnalysisEngine.ValidateConfiguration(config, out string errorMessage))
            {
                MessageBox.Show(errorMessage, "Configuration Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return null;
            }

            return config;
        }

        private void DisplayResults(AnalysisResult result)
        {
            if (result == null) return;

            // 显示概览
            DisplayOverview(result);

            // 显示任务分析
            DisplayTaskAnalysis(result.TaskAnalyses);

            // 显示中断分析
            DisplayInterruptAnalysis(result.InterruptAnalyses);

            // 显示警告
            DisplayWarnings(result.Warnings);
        }

        private void DisplayOverview(AnalysisResult result)
        {
            var overviewTextBox = this.Controls.Find("overviewText", true).FirstOrDefault() as RichTextBox;
            if (overviewTextBox == null) return;

            var sb = new StringBuilder();
            sb.AppendLine("=== AUTOSAR Stack Size Analysis Report ===");
            sb.AppendLine();
            sb.AppendLine($"Project Path: {result.ProjectPath}");
            sb.AppendLine($"Architecture: {result.Architecture}");
            sb.AppendLine($"Analysis Time: {result.AnalysisTime:yyyy-MM-dd HH:mm:ss}");
            sb.AppendLine();

            if (result.Statistics != null)
            {
                sb.AppendLine("=== Statistics ===");
                sb.AppendLine($"Total Functions: {result.Statistics.FunctionCount}");
                sb.AppendLine($"Tasks: {result.Statistics.TaskCount}");
                sb.AppendLine($"Interrupts: {result.Statistics.InterruptCount}");
                sb.AppendLine($"Recursive Functions: {result.Statistics.RecursiveFunctionCount}");
                sb.AppendLine($"Max Call Depth: {result.Statistics.MaxCallDepth}");
                sb.AppendLine($"Average Stack Size: {result.Statistics.AverageStackSize:F1} bytes");
                sb.AppendLine($"Analysis Time: {result.Statistics.AnalysisTimeMs} ms");
                sb.AppendLine();
            }

            sb.AppendLine("=== Summary ===");
            sb.AppendLine($"Maximum Stack Usage: {result.GetMaxStackUsage()} bytes");
            sb.AppendLine($"Total Stack Requirement: {result.GetTotalStackRequirement()} bytes");
            sb.AppendLine();

            if (result.Warnings.Any())
            {
                sb.AppendLine("=== Warning Summary ===");
                var warningGroups = result.Warnings.GroupBy(w => w.Level);
                foreach (var group in warningGroups)
                {
                    sb.AppendLine($"{group.Key}: {group.Count()} warnings");
                }
            }

            overviewTextBox.Text = sb.ToString();
        }

        private void DisplayTaskAnalysis(List<TaskStackAnalysis> taskAnalyses)
        {
            var tasksListView = this.Controls.Find("tasksList", true).FirstOrDefault() as ListView;
            if (tasksListView == null) return;

            tasksListView.Items.Clear();

            foreach (var task in taskAnalyses)
            {
                var item = new ListViewItem(task.TaskName ?? "Unknown");
                item.SubItems.Add($"{task.MaxStackUsage} bytes");
                item.SubItems.Add($"{task.ConfiguredStackSize} bytes");
                item.SubItems.Add($"{task.StackUtilization:P1}");

                string status = task.HasOverflowRisk ? "OVERFLOW RISK" :
                               task.StackUtilization > 0.8 ? "HIGH USAGE" : "OK";
                item.SubItems.Add(status);

                if (task.HasOverflowRisk)
                    item.BackColor = Color.LightCoral;
                else if (task.StackUtilization > 0.8)
                    item.BackColor = Color.LightYellow;

                tasksListView.Items.Add(item);
            }
        }

        private void DisplayInterruptAnalysis(List<InterruptStackAnalysis> interruptAnalyses)
        {
            var interruptsListView = this.Controls.Find("interruptsList", true).FirstOrDefault() as ListView;
            if (interruptsListView == null) return;

            interruptsListView.Items.Clear();

            foreach (var interrupt in interruptAnalyses)
            {
                var item = new ListViewItem(interrupt.InterruptName ?? "Unknown");
                item.SubItems.Add(interrupt.Priority.ToString());
                item.SubItems.Add($"{interrupt.MaxStackUsage} bytes");
                item.SubItems.Add($"{interrupt.ContextSaveSize} bytes");
                item.SubItems.Add(interrupt.HandlerFunction?.Name ?? "Unknown");

                interruptsListView.Items.Add(item);
            }
        }

        private void DisplayWarnings(List<StackWarning> warnings)
        {
            var warningsListView = this.Controls.Find("warningsList", true).FirstOrDefault() as ListView;
            if (warningsListView == null) return;

            warningsListView.Items.Clear();

            foreach (var warning in warnings)
            {
                var item = new ListViewItem(warning.Level.ToString());
                item.SubItems.Add(warning.Type.ToString());
                item.SubItems.Add(warning.Message ?? "");
                item.SubItems.Add(warning.RelatedFunction?.Name ?? "");
                item.SubItems.Add(warning.Suggestion ?? "");

                // 根据警告级别设置颜色
                switch (warning.Level)
                {
                    case WarningLevel.Critical:
                        item.BackColor = Color.LightCoral;
                        break;
                    case WarningLevel.Error:
                        item.BackColor = Color.LightSalmon;
                        break;
                    case WarningLevel.Warning:
                        item.BackColor = Color.LightYellow;
                        break;
                    case WarningLevel.Info:
                        item.BackColor = Color.LightBlue;
                        break;
                }

                warningsListView.Items.Add(item);
            }
        }

        private void GenerateReportButton_Click(object sender, EventArgs e)
        {
            if (_lastResult == null)
            {
                MessageBox.Show("No analysis results available. Please run analysis first.", "No Data", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            using (var dialog = new SaveFileDialog())
            {
                dialog.Filter = "HTML Report (*.html)|*.html|Text Report (*.txt)|*.txt|CSV Report (*.csv)|*.csv|JSON Report (*.json)|*.json|DOT Graph (*.dot)|*.dot|Mermaid Graph (*.mmd)|*.mmd";
                dialog.Title = "Save Analysis Report";
                dialog.FileName = $"StackAnalysisReport_{DateTime.Now:yyyyMMdd_HHmmss}";

                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        var reportGenerator = new ReportGenerator();
                        string extension = Path.GetExtension(dialog.FileName).ToLower();

                        switch (extension)
                        {
                            case ".html":
                                reportGenerator.GenerateHtmlReport(_lastResult, dialog.FileName);
                                break;
                            case ".txt":
                                reportGenerator.GenerateTextReport(_lastResult, dialog.FileName);
                                break;
                            case ".csv":
                                reportGenerator.GenerateCsvReport(_lastResult, dialog.FileName);
                                break;
                            case ".json":
                                reportGenerator.GenerateJsonReport(_lastResult, dialog.FileName);
                                break;
                            case ".dot":
                                reportGenerator.GenerateCallGraphDot(_lastResult, dialog.FileName);
                                break;
                            case ".mmd":
                                reportGenerator.GenerateCallGraphMermaid(_lastResult, dialog.FileName);
                                break;
                            default:
                                reportGenerator.GenerateHtmlReport(_lastResult, dialog.FileName);
                                break;
                        }

                        MessageBox.Show($"Report generated successfully:\n{dialog.FileName}", "Report Generated", MessageBoxButtons.OK, MessageBoxIcon.Information);

                        // 询问是否打开报告
                        if (extension == ".html" && MessageBox.Show("Would you like to open the report?", "Open Report", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                        {
                            System.Diagnostics.Process.Start(dialog.FileName);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Failed to generate report: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }
    }
}
