{"Version": 1, "WorkspaceRootPath": "X:\\Tools\\CalculateStackSize\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{DA24C8BC-3350-4B54-97AB-6585EA215E61}|CalculateStackSize.csproj|x:\\tools\\calculatestacksize\\core\\analysis\\recursiondetector.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA24C8BC-3350-4B54-97AB-6585EA215E61}|CalculateStackSize.csproj|solutionrelative:core\\analysis\\recursiondetector.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA24C8BC-3350-4B54-97AB-6585EA215E61}|CalculateStackSize.csproj|X:\\Tools\\CalculateStackSize\\form1.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{DA24C8BC-3350-4B54-97AB-6585EA215E61}|CalculateStackSize.csproj|solutionrelative:form1.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{DA24C8BC-3350-4B54-97AB-6585EA215E61}|CalculateStackSize.csproj|X:\\Tools\\CalculateStackSize\\models\\callgraph.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA24C8BC-3350-4B54-97AB-6585EA215E61}|CalculateStackSize.csproj|solutionrelative:models\\callgraph.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 5, "Children": [{"$type": "Bookmark", "Name": "ST:129:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:128:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:0:0:{6324226f-61b6-4f28-92ee-18d4b5fe1e48}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "CallGraph.cs", "DocumentMoniker": "X:\\Tools\\CalculateStackSize\\Models\\CallGraph.cs", "RelativeDocumentMoniker": "Models\\CallGraph.cs", "ToolTip": "X:\\Tools\\CalculateStackSize\\Models\\CallGraph.cs", "RelativeToolTip": "Models\\CallGraph.cs", "ViewState": "AgIAAMQAAAAAAAAAAAAhwN4AAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-30T13:40:35.462Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "RecursionDetector.cs", "DocumentMoniker": "X:\\Tools\\CalculateStackSize\\Core\\Analysis\\RecursionDetector.cs", "RelativeDocumentMoniker": "Core\\Analysis\\RecursionDetector.cs", "ToolTip": "X:\\Tools\\CalculateStackSize\\Core\\Analysis\\RecursionDetector.cs", "RelativeToolTip": "Core\\Analysis\\RecursionDetector.cs", "ViewState": "AgIAAAQBAAAAAAAAAAAAACMBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-30T13:40:30.891Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "Form1.cs [设计]", "DocumentMoniker": "X:\\Tools\\CalculateStackSize\\Form1.cs", "RelativeDocumentMoniker": "Form1.cs", "ToolTip": "X:\\Tools\\CalculateStackSize\\Form1.cs [设计]", "RelativeToolTip": "Form1.cs [设计]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T11:33:09.191Z", "EditorCaption": " [设计]"}]}]}]}