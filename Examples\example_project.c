/*
 * Example AUTOSAR project for stack size analysis
 * This file demonstrates various scenarios that the analyzer should handle
 */

#include <stdint.h>
#include <stdbool.h>

/* Global variables */
static uint32_t g_counter = 0;
static bool g_system_ready = false;

/* Function prototypes */
void TaskMain_Entry(void);
void TaskPeriodic_Entry(void);
void TaskBackground_Entry(void);
void Timer_ISR_Handler(void);
void Can_ISR_Handler(void);
void Adc_ISR_Handler(void);

/* Utility functions */
static void InitializeSystem(void);
static void ProcessData(uint8_t* data, uint16_t length);
static uint32_t CalculateChecksum(const uint8_t* buffer, uint16_t size);
static void SendCanMessage(uint32_t id, const uint8_t* data, uint8_t length);
static void LogError(uint16_t error_code, const char* message);

/* Recursive function example */
static uint32_t Fibon<PERSON>ci(uint32_t n);
static void RecursiveFunction(int depth);

/*
 * Main Task Entry Point
 * Priority: 10, Stack: 2048 bytes
 */
void TaskMain_Entry(void)
{
    uint8_t local_buffer[256];  // Large local variable
    uint16_t data_length = 0;
    uint32_t checksum = 0;
    
    InitializeSystem();
    
    while (1)
    {
        if (g_system_ready)
        {
            // Process incoming data
            ProcessData(local_buffer, data_length);
            
            // Calculate checksum
            checksum = CalculateChecksum(local_buffer, data_length);
            
            // Send CAN message
            SendCanMessage(0x123, local_buffer, 8);
            
            g_counter++;
        }
        
        // Task delay simulation
        for (volatile int i = 0; i < 1000; i++);
    }
}

/*
 * Periodic Task Entry Point
 * Priority: 5, Stack: 1024 bytes
 */
void TaskPeriodic_Entry(void)
{
    uint32_t sensor_data[16];  // Array of sensor readings
    uint8_t i;
    
    while (1)
    {
        // Read sensor data
        for (i = 0; i < 16; i++)
        {
            sensor_data[i] = ReadSensorValue(i);
        }
        
        // Process sensor data
        ProcessSensorData(sensor_data, 16);
        
        // Calculate Fibonacci number (recursive call example)
        uint32_t fib_result = Fibonacci(10);
        
        // Log result
        LogError(0, "Periodic task completed");
        
        // Task delay
        for (volatile int j = 0; j < 500; j++);
    }
}

/*
 * Background Task Entry Point
 * Priority: 1, Stack: 512 bytes
 */
void TaskBackground_Entry(void)
{
    uint8_t status = 0;
    
    while (1)
    {
        // Background processing
        status = PerformBackgroundWork();
        
        if (status != 0)
        {
            LogError(status, "Background task error");
        }
        
        // Long delay for background task
        for (volatile int k = 0; k < 10000; k++);
    }
}

/*
 * Timer Interrupt Service Routine
 * Priority: 15, Vector: 10
 */
void Timer_ISR_Handler(void)
{
    static uint32_t timer_counter = 0;
    uint16_t temp_value;
    
    timer_counter++;
    
    // Simple processing in ISR
    temp_value = ReadTimerValue();
    UpdateTimerStatus(temp_value);
    
    // Set system ready flag
    if (timer_counter > 100)
    {
        g_system_ready = true;
    }
}

/*
 * CAN Interrupt Service Routine
 * Priority: 12, Vector: 20
 */
void Can_ISR_Handler(void)
{
    uint8_t can_data[8];
    uint32_t can_id;
    uint8_t can_length;
    
    // Read CAN message
    if (ReadCanMessage(&can_id, can_data, &can_length))
    {
        // Process CAN message
        ProcessCanMessage(can_id, can_data, can_length);
    }
    
    // Clear interrupt flag
    ClearCanInterrupt();
}

/*
 * ADC Interrupt Service Routine
 * Priority: 8, Vector: 30
 */
void Adc_ISR_Handler(void)
{
    uint16_t adc_values[4];  // Multiple ADC channels
    uint8_t channel;
    
    // Read all ADC channels
    for (channel = 0; channel < 4; channel++)
    {
        adc_values[channel] = ReadAdcChannel(channel);
    }
    
    // Store ADC values
    StoreAdcValues(adc_values, 4);
    
    // Trigger data processing
    TriggerDataProcessing();
}

/*
 * System initialization function
 */
static void InitializeSystem(void)
{
    uint8_t init_data[64];  // Initialization buffer
    uint16_t i;
    
    // Initialize hardware
    InitializeHardware();
    
    // Clear initialization buffer
    for (i = 0; i < 64; i++)
    {
        init_data[i] = 0;
    }
    
    // Configure system parameters
    ConfigureSystemParameters(init_data, 64);
    
    g_system_ready = false;
}

/*
 * Data processing function with deep call chain
 */
static void ProcessData(uint8_t* data, uint16_t length)
{
    uint8_t processed_data[128];  // Local processing buffer
    uint16_t processed_length;
    
    // Validate input
    if (ValidateData(data, length))
    {
        // Transform data
        processed_length = TransformData(data, length, processed_data, 128);
        
        // Filter data
        FilterData(processed_data, processed_length);
        
        // Compress data
        CompressData(processed_data, processed_length);
    }
}

/*
 * Checksum calculation function
 */
static uint32_t CalculateChecksum(const uint8_t* buffer, uint16_t size)
{
    uint32_t checksum = 0;
    uint16_t i;
    
    for (i = 0; i < size; i++)
    {
        checksum += buffer[i];
        checksum = (checksum << 1) | (checksum >> 31);  // Rotate left
    }
    
    return checksum;
}

/*
 * CAN message sending function
 */
static void SendCanMessage(uint32_t id, const uint8_t* data, uint8_t length)
{
    uint8_t can_frame[16];  // CAN frame buffer
    uint8_t frame_length;
    
    // Build CAN frame
    frame_length = BuildCanFrame(id, data, length, can_frame, 16);
    
    // Transmit frame
    TransmitCanFrame(can_frame, frame_length);
}

/*
 * Error logging function
 */
static void LogError(uint16_t error_code, const char* message)
{
    char log_buffer[128];  // Log message buffer
    uint16_t timestamp;
    
    timestamp = GetTimestamp();
    
    // Format log message
    FormatLogMessage(timestamp, error_code, message, log_buffer, 128);
    
    // Write to log
    WriteToLog(log_buffer);
}

/*
 * Recursive Fibonacci function (example of recursion)
 */
static uint32_t Fibonacci(uint32_t n)
{
    if (n <= 1)
        return n;
    
    return Fibonacci(n - 1) + Fibonacci(n - 2);  // Recursive calls
}

/*
 * Another recursive function example
 */
static void RecursiveFunction(int depth)
{
    uint8_t local_array[32];  // Local array in recursive function
    int i;
    
    if (depth <= 0)
        return;
    
    // Initialize local array
    for (i = 0; i < 32; i++)
    {
        local_array[i] = (uint8_t)(depth + i);
    }
    
    // Process array
    ProcessArray(local_array, 32);
    
    // Recursive call
    RecursiveFunction(depth - 1);
}

/* Stub functions for compilation */
uint32_t ReadSensorValue(uint8_t sensor_id) { return sensor_id * 100; }
void ProcessSensorData(uint32_t* data, uint8_t count) { (void)data; (void)count; }
uint8_t PerformBackgroundWork(void) { return 0; }
uint16_t ReadTimerValue(void) { return 1000; }
void UpdateTimerStatus(uint16_t value) { (void)value; }
bool ReadCanMessage(uint32_t* id, uint8_t* data, uint8_t* length) { *id = 0x100; *length = 8; return true; }
void ProcessCanMessage(uint32_t id, uint8_t* data, uint8_t length) { (void)id; (void)data; (void)length; }
void ClearCanInterrupt(void) { }
uint16_t ReadAdcChannel(uint8_t channel) { return channel * 1000; }
void StoreAdcValues(uint16_t* values, uint8_t count) { (void)values; (void)count; }
void TriggerDataProcessing(void) { }
void InitializeHardware(void) { }
void ConfigureSystemParameters(uint8_t* data, uint16_t length) { (void)data; (void)length; }
bool ValidateData(uint8_t* data, uint16_t length) { (void)data; (void)length; return true; }
uint16_t TransformData(uint8_t* input, uint16_t input_len, uint8_t* output, uint16_t output_len) { (void)input; (void)input_len; (void)output; (void)output_len; return 0; }
void FilterData(uint8_t* data, uint16_t length) { (void)data; (void)length; }
void CompressData(uint8_t* data, uint16_t length) { (void)data; (void)length; }
uint8_t BuildCanFrame(uint32_t id, const uint8_t* data, uint8_t length, uint8_t* frame, uint8_t frame_size) { (void)id; (void)data; (void)length; (void)frame; (void)frame_size; return 8; }
void TransmitCanFrame(uint8_t* frame, uint8_t length) { (void)frame; (void)length; }
uint16_t GetTimestamp(void) { return 12345; }
void FormatLogMessage(uint16_t timestamp, uint16_t error_code, const char* message, char* buffer, uint16_t buffer_size) { (void)timestamp; (void)error_code; (void)message; (void)buffer; (void)buffer_size; }
void WriteToLog(const char* message) { (void)message; }
void ProcessArray(uint8_t* array, uint8_t size) { (void)array; (void)size; }
