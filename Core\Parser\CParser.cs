using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using CalculateStackSize.Models;

namespace CalculateStackSize.Core.Parser
{
    /// <summary>
    /// C语言源码解析器
    /// </summary>
    public class CParser
    {
        private readonly Dictionary<string, int> _typeSizes;
        private readonly List<string> _includePaths;
        private readonly HashSet<string> _processedFiles;

        public CParser()
        {
            _typeSizes = InitializeTypeSizes();
            _includePaths = new List<string>();
            _processedFiles = new HashSet<string>();
        }

        /// <summary>
        /// 解析C源文件
        /// </summary>
        public List<Function> ParseSourceFile(string filePath)
        {
            if (_processedFiles.Contains(filePath))
                return new List<Function>();

            _processedFiles.Add(filePath);
            
            if (!File.Exists(filePath))
                throw new FileNotFoundException($"Source file not found: {filePath}");

            string content = File.ReadAllText(filePath);
            content = PreprocessCode(content);
            
            var functions = new List<Function>();
            
            // 解析函数定义
            var functionMatches = FindFunctionDefinitions(content);
            
            foreach (Match match in functionMatches)
            {
                var function = ParseFunctionDefinition(match, filePath);
                if (function != null)
                {
                    functions.Add(function);
                }
            }
            
            // 解析函数调用关系
            foreach (var function in functions)
            {
                ParseFunctionCalls(function, content);
            }
            
            return functions;
        }

        /// <summary>
        /// 预处理代码（简化版）
        /// </summary>
        private string PreprocessCode(string content)
        {
            // 移除注释
            content = RemoveComments(content);
            
            // 处理简单的宏定义（这里只是基础实现）
            content = ProcessSimpleMacros(content);
            
            return content;
        }

        /// <summary>
        /// 移除C语言注释
        /// </summary>
        private string RemoveComments(string content)
        {
            // 移除单行注释
            content = Regex.Replace(content, @"//.*$", "", RegexOptions.Multiline);
            
            // 移除多行注释
            content = Regex.Replace(content, @"/\*.*?\*/", "", RegexOptions.Singleline);
            
            return content;
        }

        /// <summary>
        /// 处理简单的宏定义
        /// </summary>
        private string ProcessSimpleMacros(string content)
        {
            // 这里只处理简单的#define宏，复杂的宏处理需要更完整的预处理器
            var definePattern = @"#define\s+(\w+)\s+(.+)";
            var defines = new Dictionary<string, string>();
            
            var matches = Regex.Matches(content, definePattern);
            foreach (Match match in matches)
            {
                defines[match.Groups[1].Value] = match.Groups[2].Value.Trim();
            }
            
            // 替换宏
            foreach (var define in defines)
            {
                content = Regex.Replace(content, $@"\b{define.Key}\b", define.Value);
            }
            
            // 移除#define行
            content = Regex.Replace(content, definePattern, "", RegexOptions.Multiline);
            
            return content;
        }

        /// <summary>
        /// 查找函数定义
        /// </summary>
        private MatchCollection FindFunctionDefinitions(string content)
        {
            // 匹配函数定义的正则表达式
            // 这是一个简化版本，实际的C语言解析会更复杂
            var pattern = @"(?:^|\n)\s*(?:static\s+|extern\s+|inline\s+)*" +
                         @"(?:void|int|char|short|long|float|double|unsigned\s+\w+|\w+\s*\*?)\s+" +
                         @"(\w+)\s*\(([^)]*)\)\s*\{";
            
            return Regex.Matches(content, pattern, RegexOptions.Multiline | RegexOptions.IgnoreCase);
        }

        /// <summary>
        /// 解析函数定义
        /// </summary>
        private Function ParseFunctionDefinition(Match match, string filePath)
        {
            try
            {
                string functionName = match.Groups[1].Value;
                string parameters = match.Groups[2].Value;
                
                var function = new Function
                {
                    Name = functionName,
                    SourceFile = filePath,
                    LineNumber = GetLineNumber(match.Index, File.ReadAllText(filePath)),
                    Type = DetermineFunctionType(functionName)
                };
                
                // 解析参数
                function.Parameters = ParseParameters(parameters);
                function.ParameterSize = CalculateParameterSize(function.Parameters);
                
                // 解析函数体中的局部变量
                string functionBody = ExtractFunctionBody(match, File.ReadAllText(filePath));
                function.LocalVariables = ParseLocalVariables(functionBody);
                function.LocalVariableSize = CalculateLocalVariableSize(function.LocalVariables);
                
                // 估算寄存器保存大小（这里使用默认值，实际需要根据架构和函数内容分析）
                function.RegisterSaveSize = EstimateRegisterSaveSize(function);
                
                return function;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error parsing function definition: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 确定函数类型
        /// </summary>
        private FunctionType DetermineFunctionType(string functionName)
        {
            // 根据函数名称模式判断函数类型
            if (functionName.EndsWith("_ISR") || functionName.StartsWith("ISR_") || 
                functionName.Contains("Interrupt") || functionName.Contains("Handler"))
            {
                return FunctionType.InterruptHandler;
            }
            
            if (functionName.StartsWith("Task_") || functionName.EndsWith("_Task") ||
                functionName.Contains("TaskMain"))
            {
                return FunctionType.TaskFunction;
            }
            
            if (functionName.StartsWith("Hook_") || functionName.EndsWith("_Hook"))
            {
                return FunctionType.HookFunction;
            }
            
            return FunctionType.Normal;
        }

        /// <summary>
        /// 解析函数参数
        /// </summary>
        private List<Parameter> ParseParameters(string parametersString)
        {
            var parameters = new List<Parameter>();
            
            if (string.IsNullOrWhiteSpace(parametersString) || parametersString.Trim() == "void")
                return parameters;
            
            var paramParts = parametersString.Split(',');
            
            foreach (var part in paramParts)
            {
                var trimmed = part.Trim();
                if (string.IsNullOrEmpty(trimmed)) continue;
                
                var param = ParseSingleParameter(trimmed);
                if (param != null)
                {
                    parameters.Add(param);
                }
            }
            
            return parameters;
        }

        /// <summary>
        /// 解析单个参数
        /// </summary>
        private Parameter ParseSingleParameter(string paramString)
        {
            // 简化的参数解析
            var parts = paramString.Trim().Split(new[] { ' ', '\t' }, StringSplitOptions.RemoveEmptyEntries);
            
            if (parts.Length < 2) return null;
            
            string type = string.Join(" ", parts.Take(parts.Length - 1));
            string name = parts.Last();
            
            // 移除可能的指针符号
            if (name.StartsWith("*"))
            {
                type += "*";
                name = name.Substring(1);
            }
            
            return new Parameter
            {
                Name = name,
                Type = type,
                Size = GetTypeSize(type),
                PassingMethod = DetermineParameterPassingMethod(type)
            };
        }

        /// <summary>
        /// 解析局部变量
        /// </summary>
        private List<LocalVariable> ParseLocalVariables(string functionBody)
        {
            var variables = new List<LocalVariable>();
            
            // 匹配变量声明的正则表达式
            var pattern = @"(?:^|\n|\{|\;)\s*(?:static\s+|const\s+|volatile\s+)*" +
                         @"((?:unsigned\s+|signed\s+)?(?:char|short|int|long|float|double|\w+)(?:\s*\*)*)\s+" +
                         @"(\w+)(?:\[(\d+)\])?\s*(?:=|;)";
            
            var matches = Regex.Matches(functionBody, pattern, RegexOptions.Multiline);
            
            foreach (Match match in matches)
            {
                string type = match.Groups[1].Value.Trim();
                string name = match.Groups[2].Value;
                string arraySize = match.Groups[3].Value;
                
                var variable = new LocalVariable
                {
                    Name = name,
                    Type = type,
                    Size = GetTypeSize(type),
                    IsArray = !string.IsNullOrEmpty(arraySize)
                };
                
                if (variable.IsArray && int.TryParse(arraySize, out int size))
                {
                    variable.ArraySize = size;
                    variable.Size *= size;
                }
                
                variables.Add(variable);
            }
            
            return variables;
        }

        /// <summary>
        /// 解析函数调用
        /// </summary>
        private void ParseFunctionCalls(Function function, string content)
        {
            string functionBody = ExtractFunctionBodyByName(function.Name, content);
            
            // 匹配函数调用的正则表达式
            var pattern = @"(\w+)\s*\(";
            var matches = Regex.Matches(functionBody, pattern);
            
            foreach (Match match in matches)
            {
                string calledFunctionName = match.Groups[1].Value;
                
                // 排除关键字和当前函数名
                if (IsKeyword(calledFunctionName) || calledFunctionName == function.Name)
                    continue;
                
                // 这里暂时创建一个占位符函数，实际应该在调用图构建时解析
                var calledFunction = new Function { Name = calledFunctionName };
                function.AddCalledFunction(calledFunction, GetLineNumber(match.Index, functionBody));
            }
        }

        // 辅助方法
        private Dictionary<string, int> InitializeTypeSizes()
        {
            return new Dictionary<string, int>
            {
                ["char"] = 1,
                ["unsigned char"] = 1,
                ["signed char"] = 1,
                ["short"] = 2,
                ["unsigned short"] = 2,
                ["int"] = 4,
                ["unsigned int"] = 4,
                ["long"] = 4,
                ["unsigned long"] = 4,
                ["long long"] = 8,
                ["unsigned long long"] = 8,
                ["float"] = 4,
                ["double"] = 8,
                ["void*"] = 4, // 假设32位系统
                ["char*"] = 4,
                ["int*"] = 4
            };
        }

        private int GetTypeSize(string type)
        {
            type = type.Trim();
            
            if (_typeSizes.ContainsKey(type))
                return _typeSizes[type];
            
            // 处理指针类型
            if (type.EndsWith("*"))
                return 4; // 假设32位指针
            
            // 默认大小
            return 4;
        }

        private ParameterPassingMethod DetermineParameterPassingMethod(string type)
        {
            // 简化的参数传递方式判断
            // 实际需要根据具体的调用约定来确定
            int size = GetTypeSize(type);
            return size <= 4 ? ParameterPassingMethod.Register : ParameterPassingMethod.Stack;
        }

        private int CalculateParameterSize(List<Parameter> parameters)
        {
            return parameters.Where(p => p.PassingMethod == ParameterPassingMethod.Stack)
                           .Sum(p => p.Size);
        }

        private int CalculateLocalVariableSize(List<LocalVariable> variables)
        {
            return variables.Sum(v => v.Size);
        }

        private int EstimateRegisterSaveSize(Function function)
        {
            // 简化的寄存器保存大小估算
            // 实际需要根据函数内容和目标架构来确定
            return 16; // 默认保存4个寄存器
        }

        private string ExtractFunctionBody(Match functionMatch, string content)
        {
            int startIndex = functionMatch.Index + functionMatch.Length;
            int braceCount = 1;
            int currentIndex = startIndex;
            
            while (currentIndex < content.Length && braceCount > 0)
            {
                if (content[currentIndex] == '{')
                    braceCount++;
                else if (content[currentIndex] == '}')
                    braceCount--;
                
                currentIndex++;
            }
            
            return content.Substring(startIndex, currentIndex - startIndex - 1);
        }

        private string ExtractFunctionBodyByName(string functionName, string content)
        {
            var pattern = $@"\b{functionName}\s*\([^)]*\)\s*\{{";
            var match = Regex.Match(content, pattern);
            
            if (match.Success)
            {
                return ExtractFunctionBody(match, content);
            }
            
            return "";
        }

        private int GetLineNumber(int index, string content)
        {
            return content.Substring(0, index).Count(c => c == '\n') + 1;
        }

        private bool IsKeyword(string word)
        {
            var keywords = new HashSet<string>
            {
                "if", "else", "while", "for", "do", "switch", "case", "default",
                "return", "break", "continue", "goto", "sizeof", "typeof"
            };
            
            return keywords.Contains(word);
        }
    }
}
