<?xml version="1.0" encoding="UTF-8"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <AR-PACKAGES>
    <AR-PACKAGE>
      <SHORT-NAME>OS</SHORT-NAME>
      <ELEMENTS>
        <!-- OS Configuration -->
        <OS>
          <SHORT-NAME>OsApplication</SHORT-NAME>
          <OS-TASKS>
            <!-- Task Definitions -->
            <OS-TASK>
              <SHORT-NAME>TaskMain</SHORT-NAME>
              <TASK-ID>1</TASK-ID>
              <PRIORITY>10</PRIORITY>
              <STACK-SIZE>2048</STACK-SIZE>
              <ENTRY-POINT>TaskMain_Entry</ENTRY-POINT>
              <AUTOSTART>true</AUTOSTART>
            </OS-TASK>
            
            <OS-TASK>
              <SHORT-NAME>TaskPeriodic</SHORT-NAME>
              <TASK-ID>2</TASK-ID>
              <PRIORITY>5</PRIORITY>
              <STACK-SIZE>1024</STACK-SIZE>
              <ENTRY-POINT>TaskPeriodic_Entry</ENTRY-POINT>
              <AUTOSTART>false</AUTOSTART>
            </OS-TASK>
            
            <OS-TASK>
              <SHORT-NAME>TaskBackground</SHORT-NAME>
              <TASK-ID>3</TASK-ID>
              <PRIORITY>1</PRIORITY>
              <STACK-SIZE>512</STACK-SIZE>
              <ENTRY-POINT>TaskBackground_Entry</ENTRY-POINT>
              <AUTOSTART>true</AUTOSTART>
            </OS-TASK>
          </OS-TASKS>
          
          <!-- ISR Definitions -->
          <OS-ISRS>
            <OS-ISR>
              <SHORT-NAME>TimerISR</SHORT-NAME>
              <VECTOR>10</VECTOR>
              <PRIORITY>15</PRIORITY>
              <HANDLER>Timer_ISR_Handler</HANDLER>
              <CATEGORY>2</CATEGORY>
            </OS-ISR>
            
            <OS-ISR>
              <SHORT-NAME>CanISR</SHORT-NAME>
              <VECTOR>20</VECTOR>
              <PRIORITY>12</PRIORITY>
              <HANDLER>Can_ISR_Handler</HANDLER>
              <CATEGORY>2</CATEGORY>
            </OS-ISR>
            
            <OS-ISR>
              <SHORT-NAME>AdcISR</SHORT-NAME>
              <VECTOR>30</VECTOR>
              <PRIORITY>8</PRIORITY>
              <HANDLER>Adc_ISR_Handler</HANDLER>
              <CATEGORY>2</CATEGORY>
            </OS-ISR>
          </OS-ISRS>
          
          <!-- Stack Configurations -->
          <OS-STACKS>
            <OS-STACK>
              <SHORT-NAME>SystemStack</SHORT-NAME>
              <SIZE>4096</SIZE>
              <TYPE>System</TYPE>
            </OS-STACK>
            
            <OS-STACK>
              <SHORT-NAME>InterruptStack</SHORT-NAME>
              <SIZE>2048</SIZE>
              <TYPE>ISR</TYPE>
            </OS-STACK>
          </OS-STACKS>
        </OS>
      </ELEMENTS>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
