# AUTOSAR Stack Size Calculator

A comprehensive tool for analyzing stack usage in AUTOSAR embedded projects, supporting RH850 and TC377 processor architectures.

## Features

### Core Analysis Capabilities
- **Static Code Analysis**: Parses C source files to extract function definitions, variables, and call relationships
- **Call Graph Construction**: Builds complete function call graphs with recursion detection
- **Stack Usage Calculation**: Computes stack requirements for tasks and interrupt service routines
- **AUTOSAR Integration**: Supports AUTOSAR OS configuration files (XML, OIL, C header formats)
- **Multi-Architecture Support**: Specialized handling for RH850 V850 and TC377 TriCore architectures

### Advanced Features
- **Assembly Code Analysis**: Parses assembly files to refine stack usage calculations
- **Recursion Detection**: Identifies and analyzes recursive function calls with risk assessment
- **Interrupt Analysis**: Calculates interrupt nesting and context switching overhead
- **Warning System**: Comprehensive warning system for potential stack overflow risks

### Reporting and Visualization
- **Multiple Report Formats**: HTML, Text, CSV, JSON reports
- **Call Graph Visualization**: DOT and Mermaid format graph generation
- **Interactive GUI**: Windows Forms-based user interface
- **Detailed Statistics**: Comprehensive analysis statistics and metrics

## Supported Architectures

### RH850 (Renesas V850)
- V850 instruction set analysis
- RH850-specific calling conventions
- Register save/restore calculations
- Interrupt context handling

### TC377 (Infineon TriCore)
- TriCore instruction set analysis
- Address and data register handling
- Context Save Area (CSA) calculations
- Multi-core considerations

## Getting Started

### Prerequisites
- .NET Framework 4.7.2 or later
- Windows operating system
- Visual Studio 2019 or later (for development)

### Installation
1. Clone the repository or download the source code
2. Open `CalculateStackSize.sln` in Visual Studio
3. Build the solution (Build → Build Solution)
4. Run the application from `bin/Debug/CalculateStackSize.exe`

### Basic Usage

#### 1. Configure Analysis Parameters
- **Project Path**: Select the folder containing your C source files
- **Processor Architecture**: Choose between RH850 and TC377
- **AUTOSAR Config**: (Optional) Select your AUTOSAR configuration file
- **Assembly File**: (Optional) Select assembly file for enhanced analysis

#### 2. Run Analysis
- Click "Start Analysis" to begin the stack analysis process
- The tool will parse source files, build call graphs, and calculate stack usage
- Progress is shown in the status bar

#### 3. Review Results
- **Overview Tab**: Summary statistics and maximum stack usage
- **Tasks Tab**: Individual task stack analysis with overflow risk indicators
- **Interrupts Tab**: Interrupt service routine analysis
- **Warnings Tab**: Potential issues and recommendations

#### 4. Generate Reports
- Click "Generate Report" after analysis completion
- Choose from multiple report formats:
  - HTML: Interactive web-based report
  - Text: Simple text summary
  - CSV: Data for spreadsheet analysis
  - JSON: Machine-readable format
  - DOT: Graphviz call graph
  - Mermaid: Mermaid.js call graph

## Configuration File Formats

### AUTOSAR XML Configuration
```xml
<OS>
  <OS-TASKS>
    <OS-TASK>
      <SHORT-NAME>TaskMain</SHORT-NAME>
      <PRIORITY>10</PRIORITY>
      <STACK-SIZE>2048</STACK-SIZE>
      <ENTRY-POINT>TaskMain_Entry</ENTRY-POINT>
    </OS-TASK>
  </OS-TASKS>
  <OS-ISRS>
    <OS-ISR>
      <SHORT-NAME>TimerISR</SHORT-NAME>
      <VECTOR>10</VECTOR>
      <PRIORITY>15</PRIORITY>
      <HANDLER>Timer_ISR_Handler</HANDLER>
    </OS-ISR>
  </OS-ISRS>
</OS>
```

### OIL Configuration
```oil
TASK TaskMain {
    PRIORITY = 10;
    STACKSIZE = 2048;
    AUTOSTART = TRUE;
};

ISR TimerISR {
    CATEGORY = 2;
    PRIORITY = 15;
    VECTOR = 10;
};
```

## Example Project Structure
```
MyProject/
├── src/
│   ├── main.c
│   ├── tasks.c
│   ├── interrupts.c
│   └── utils.c
├── config/
│   ├── autosar_config.xml
│   └── os_config.oil
└── asm/
    └── startup.s
```

## Understanding Results

### Stack Usage Calculation
The tool calculates stack usage by analyzing:
- **Local Variables**: Size of function-local variables
- **Function Parameters**: Parameters passed on the stack
- **Register Saves**: Processor-specific register preservation
- **Call Chain Depth**: Maximum depth of function calls
- **Interrupt Context**: Additional overhead for interrupt handling

### Warning Types
- **Stack Overflow**: Calculated usage exceeds configured stack size
- **High Usage**: Stack utilization above 80%
- **Recursive Calls**: Functions with recursive call patterns
- **Large Local Variables**: Functions with excessive local variable usage
- **Deep Call Chains**: Long sequences of function calls

### Risk Assessment
- **Critical**: Immediate attention required (stack overflow likely)
- **High**: Significant risk of stack overflow
- **Medium**: Potential issues under certain conditions
- **Low**: Minor concerns or informational

## Advanced Features

### Assembly Analysis
When assembly files are provided, the tool performs additional analysis:
- Instruction-level stack impact calculation
- Hardware-specific stack operations
- Inline assembly integration with C code analysis

### Recursion Detection
The tool identifies and analyzes:
- Direct recursion (function calls itself)
- Indirect recursion (mutual recursion between functions)
- Recursion depth estimation
- Risk assessment based on recursion patterns

### Multi-Architecture Support
Architecture-specific features:
- **RH850**: V850 instruction set, 4-byte alignment, specific calling conventions
- **TC377**: TriCore instruction set, 8-byte alignment, CSA handling

## Troubleshooting

### Common Issues
1. **"Project path not found"**: Ensure the selected path contains C source files
2. **"No functions found"**: Check that source files contain valid C function definitions
3. **"AUTOSAR config parse error"**: Verify configuration file format and syntax
4. **"Assembly analysis failed"**: Assembly file format may not be supported

### Performance Considerations
- Large projects (>1000 functions) may take several minutes to analyze
- Assembly analysis significantly increases processing time
- Consider excluding test files and mock implementations

## Contributing

### Development Setup
1. Fork the repository
2. Create a feature branch
3. Implement changes with appropriate unit tests
4. Submit a pull request

### Architecture Overview
- **Core**: Analysis engines and data models
- **Processors**: Architecture-specific implementations
- **Reports**: Report generation and visualization
- **UI**: Windows Forms user interface

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For questions, issues, or feature requests:
1. Check the existing issues in the repository
2. Create a new issue with detailed information
3. Include sample code and configuration files when possible

## Version History

### v1.0.0 (Current)
- Initial release
- RH850 and TC377 architecture support
- AUTOSAR configuration parsing
- Multiple report formats
- GUI interface
- Assembly code analysis
- Recursion detection
- Call graph visualization
