using System;
using System.Collections.Generic;
using System.Linq;
using CalculateStackSize.Models;

namespace CalculateStackSize.Processors.RH850
{
    /// <summary>
    /// RH850 V850架构处理器特定实现
    /// </summary>
    public class RH850Processor : IProcessorSpecific
    {
        public ProcessorArchitecture Architecture => ProcessorArchitecture.RH850;
        public string ArchitectureName => "RH850 V850";
        public int PointerSize => 4; // 32位架构
        public int WordSize => 4;
        public int StackAlignment => 4; // 4字节对齐

        private readonly RH850CallingConvention _callingConvention;
        private readonly RH850RegisterInfo _registerInfo;

        public RH850Processor()
        {
            _callingConvention = new RH850CallingConvention();
            _registerInfo = new RH850RegisterInfo();
        }

        /// <summary>
        /// 计算函数的寄存器保存大小
        /// </summary>
        public int CalculateRegisterSaveSize(Function function)
        {
            // RH850 V850架构的寄存器保存规则
            int saveSize = 0;

            // 基本的callee-saved寄存器
            var calleeSavedRegisters = _registerInfo.GetCalleeSavedRegisters();
            
            // 根据函数特征确定需要保存的寄存器
            var registersToSave = DetermineRegistersToSave(function, calleeSavedRegisters);
            
            saveSize = registersToSave.Count * 4; // 每个寄存器4字节

            // 如果是中断处理函数，需要保存更多寄存器
            if (function.Type == FunctionType.InterruptHandler)
            {
                saveSize += CalculateInterruptContextSaveSize();
            }

            return saveSize;
        }

        /// <summary>
        /// 计算参数传递的堆栈大小
        /// </summary>
        public int CalculateParameterStackSize(List<Parameter> parameters)
        {
            return _callingConvention.CalculateParameterStackSize(parameters);
        }

        /// <summary>
        /// 确定参数传递方式
        /// </summary>
        public ParameterPassingMethod DetermineParameterPassingMethod(Parameter parameter, int parameterIndex)
        {
            return _callingConvention.DetermineParameterPassingMethod(parameter, parameterIndex);
        }

        /// <summary>
        /// 计算堆栈帧对齐
        /// </summary>
        public int CalculateStackFrameAlignment(int currentSize)
        {
            int remainder = currentSize % StackAlignment;
            return remainder == 0 ? 0 : StackAlignment - remainder;
        }

        /// <summary>
        /// 获取函数调用开销
        /// </summary>
        public int GetFunctionCallOverhead()
        {
            // RH850函数调用的基本开销
            return 4; // 返回地址
        }

        /// <summary>
        /// 分析汇编指令的堆栈影响
        /// </summary>
        public int AnalyzeAssemblyStackImpact(string assemblyInstruction)
        {
            var instruction = assemblyInstruction.Trim().ToLower();
            
            // 分析堆栈操作指令
            if (instruction.StartsWith("st.w") || instruction.StartsWith("st.h") || instruction.StartsWith("st.b"))
            {
                // 存储指令，可能影响堆栈
                return AnalyzeStoreInstruction(instruction);
            }
            else if (instruction.StartsWith("ld.w") || instruction.StartsWith("ld.h") || instruction.StartsWith("ld.b"))
            {
                // 加载指令
                return AnalyzeLoadInstruction(instruction);
            }
            else if (instruction.StartsWith("add") && instruction.Contains("sp"))
            {
                // 堆栈指针操作
                return AnalyzeStackPointerOperation(instruction);
            }
            else if (instruction.StartsWith("pushsp") || instruction.StartsWith("push"))
            {
                // 压栈操作
                return AnalyzePushInstruction(instruction);
            }
            else if (instruction.StartsWith("popsp") || instruction.StartsWith("pop"))
            {
                // 出栈操作
                return AnalyzePopInstruction(instruction);
            }

            return 0; // 不影响堆栈
        }

        /// <summary>
        /// 获取中断向量表信息
        /// </summary>
        public Dictionary<int, string> GetInterruptVectorTable()
        {
            return new Dictionary<int, string>
            {
                [0] = "Reset",
                [1] = "NMI",
                [2] = "HardFault",
                [3] = "MemManage",
                [4] = "BusFault",
                [5] = "UsageFault",
                // ... 更多中断向量
            };
        }

        /// <summary>
        /// 确定需要保存的寄存器
        /// </summary>
        private List<string> DetermineRegistersToSave(Function function, List<string> calleeSavedRegisters)
        {
            var registersToSave = new List<string>();

            // 基于函数复杂度确定需要保存的寄存器
            if (function.CalledFunctions.Count > 0)
            {
                // 如果函数调用其他函数，需要保存链接寄存器
                registersToSave.Add("r31"); // 链接寄存器
            }

            if (function.LocalVariables.Count > 4)
            {
                // 如果局部变量较多，可能需要更多寄存器
                registersToSave.AddRange(calleeSavedRegisters.Take(4));
            }
            else if (function.LocalVariables.Count > 0)
            {
                registersToSave.AddRange(calleeSavedRegisters.Take(2));
            }

            return registersToSave.Distinct().ToList();
        }

        /// <summary>
        /// 计算中断上下文保存大小
        /// </summary>
        private int CalculateInterruptContextSaveSize()
        {
            // RH850中断处理需要保存的寄存器
            var interruptSaveRegisters = new[]
            {
                "r1", "r2", "r3", "r4", "r5", "r6", "r7", "r8", "r9", "r10",
                "r11", "r12", "r13", "r14", "r15", "r16", "r17", "r18", "r19", "r20",
                "PSW", "PC", "EIPC", "EIPSW"
            };

            return interruptSaveRegisters.Length * 4; // 每个寄存器4字节
        }

        /// <summary>
        /// 分析存储指令
        /// </summary>
        private int AnalyzeStoreInstruction(string instruction)
        {
            // 简化的存储指令分析
            if (instruction.Contains("sp"))
            {
                // 涉及堆栈指针的存储操作
                if (instruction.StartsWith("st.w"))
                    return -4; // 存储4字节到堆栈
                else if (instruction.StartsWith("st.h"))
                    return -2; // 存储2字节到堆栈
                else if (instruction.StartsWith("st.b"))
                    return -1; // 存储1字节到堆栈
            }
            return 0;
        }

        /// <summary>
        /// 分析加载指令
        /// </summary>
        private int AnalyzeLoadInstruction(string instruction)
        {
            // 简化的加载指令分析
            if (instruction.Contains("sp"))
            {
                // 涉及堆栈指针的加载操作
                if (instruction.StartsWith("ld.w"))
                    return 4; // 从堆栈加载4字节
                else if (instruction.StartsWith("ld.h"))
                    return 2; // 从堆栈加载2字节
                else if (instruction.StartsWith("ld.b"))
                    return 1; // 从堆栈加载1字节
            }
            return 0;
        }

        /// <summary>
        /// 分析堆栈指针操作
        /// </summary>
        private int AnalyzeStackPointerOperation(string instruction)
        {
            // 解析add sp, immediate形式的指令
            var parts = instruction.Split(new[] { ' ', ',' }, StringSplitOptions.RemoveEmptyEntries);
            
            if (parts.Length >= 3 && parts[1] == "sp")
            {
                if (int.TryParse(parts[2], out int immediate))
                {
                    return immediate; // 正值表示堆栈增长，负值表示堆栈收缩
                }
            }
            
            return 0;
        }

        /// <summary>
        /// 分析压栈指令
        /// </summary>
        private int AnalyzePushInstruction(string instruction)
        {
            // RH850的压栈指令分析
            if (instruction.StartsWith("pushsp"))
            {
                // pushsp指令，压入多个寄存器
                return -16; // 假设压入4个寄存器
            }
            else if (instruction.StartsWith("push"))
            {
                return -4; // 压入一个寄存器
            }
            
            return 0;
        }

        /// <summary>
        /// 分析出栈指令
        /// </summary>
        private int AnalyzePopInstruction(string instruction)
        {
            // RH850的出栈指令分析
            if (instruction.StartsWith("popsp"))
            {
                // popsp指令，弹出多个寄存器
                return 16; // 假设弹出4个寄存器
            }
            else if (instruction.StartsWith("pop"))
            {
                return 4; // 弹出一个寄存器
            }
            
            return 0;
        }
    }

    /// <summary>
    /// RH850调用约定
    /// </summary>
    public class RH850CallingConvention
    {
        private readonly string[] _parameterRegisters = { "r6", "r7", "r8", "r9" };

        public int CalculateParameterStackSize(List<Parameter> parameters)
        {
            int stackSize = 0;
            int registerCount = 0;

            foreach (var parameter in parameters)
            {
                if (registerCount < _parameterRegisters.Length && parameter.Size <= 4)
                {
                    // 参数通过寄存器传递
                    registerCount++;
                }
                else
                {
                    // 参数通过堆栈传递
                    stackSize += AlignSize(parameter.Size, 4);
                }
            }

            return stackSize;
        }

        public ParameterPassingMethod DetermineParameterPassingMethod(Parameter parameter, int parameterIndex)
        {
            if (parameterIndex < _parameterRegisters.Length && parameter.Size <= 4)
            {
                return ParameterPassingMethod.Register;
            }
            else
            {
                return ParameterPassingMethod.Stack;
            }
        }

        private int AlignSize(int size, int alignment)
        {
            int remainder = size % alignment;
            return remainder == 0 ? size : size + alignment - remainder;
        }
    }

    /// <summary>
    /// RH850寄存器信息
    /// </summary>
    public class RH850RegisterInfo
    {
        public List<string> GetCalleeSavedRegisters()
        {
            return new List<string>
            {
                "r20", "r21", "r22", "r23", "r24", "r25", "r26", "r27", "r28", "r29"
            };
        }

        public List<string> GetCallerSavedRegisters()
        {
            return new List<string>
            {
                "r1", "r2", "r3", "r4", "r5", "r6", "r7", "r8", "r9", "r10",
                "r11", "r12", "r13", "r14", "r15", "r16", "r17", "r18", "r19"
            };
        }

        public List<string> GetSpecialRegisters()
        {
            return new List<string>
            {
                "r0",   // 零寄存器
                "r30",  // 堆栈指针
                "r31"   // 链接寄存器
            };
        }
    }

    /// <summary>
    /// 处理器特定接口
    /// </summary>
    public interface IProcessorSpecific
    {
        ProcessorArchitecture Architecture { get; }
        string ArchitectureName { get; }
        int PointerSize { get; }
        int WordSize { get; }
        int StackAlignment { get; }

        int CalculateRegisterSaveSize(Function function);
        int CalculateParameterStackSize(List<Parameter> parameters);
        ParameterPassingMethod DetermineParameterPassingMethod(Parameter parameter, int parameterIndex);
        int CalculateStackFrameAlignment(int currentSize);
        int GetFunctionCallOverhead();
        int AnalyzeAssemblyStackImpact(string assemblyInstruction);
        Dictionary<int, string> GetInterruptVectorTable();
    }
}
