using System;
using System.Collections.Generic;
using System.Linq;
using CalculateStackSize.Models;

namespace CalculateStackSize.Core.Analysis
{
    /// <summary>
    /// 堆栈大小计算器
    /// </summary>
    public class StackCalculator
    {
        private readonly ProcessorArchitecture _architecture;
        private readonly Dictionary<Function, StackFrame> _stackFrames;
        private readonly Dictionary<Function, int> _maxStackUsage;

        public StackCalculator(ProcessorArchitecture architecture)
        {
            _architecture = architecture;
            _stackFrames = new Dictionary<Function, StackFrame>();
            _maxStackUsage = new Dictionary<Function, int>();
        }

        /// <summary>
        /// 计算调用图中所有函数的堆栈使用情况
        /// </summary>
        public StackCalculationResult CalculateStackUsage(CallGraph callGraph)
        {
            var result = new StackCalculationResult
            {
                Architecture = _architecture,
                CalculationTime = DateTime.Now
            };

            try
            {
                // 1. 为每个函数创建堆栈帧
                CreateStackFrames(callGraph);

                // 2. 计算每个入口点的最大堆栈使用
                foreach (var entryPoint in callGraph.EntryPoints)
                {
                    var stackUsage = CalculateMaxStackUsage(entryPoint, new HashSet<Function>());
                    result.EntryPointStackUsages[entryPoint] = stackUsage;
                }

                // 3. 分析任务堆栈使用
                AnalyzeTaskStackUsage(callGraph, result);

                // 4. 分析中断堆栈使用
                AnalyzeInterruptStackUsage(callGraph, result);

                // 5. 检测堆栈使用问题
                DetectStackIssues(result);

                result.IsSuccessful = true;
            }
            catch (Exception ex)
            {
                result.IsSuccessful = false;
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        /// <summary>
        /// 为所有函数创建堆栈帧
        /// </summary>
        private void CreateStackFrames(CallGraph callGraph)
        {
            foreach (var function in callGraph.Functions.Values)
            {
                var stackFrame = new StackFrame(function, _architecture);
                _stackFrames[function] = stackFrame;
            }
        }

        /// <summary>
        /// 计算函数的最大堆栈使用量
        /// </summary>
        private int CalculateMaxStackUsage(Function function, HashSet<Function> visitedFunctions)
        {
            // 检查是否已经计算过
            if (_maxStackUsage.ContainsKey(function))
                return _maxStackUsage[function];

            // 检查递归调用
            if (visitedFunctions.Contains(function))
            {
                // 递归调用，使用估算的递归深度
                int recursiveStackUsage = _stackFrames[function].FrameSize * function.MaxRecursionDepth;
                _maxStackUsage[function] = recursiveStackUsage;
                return recursiveStackUsage;
            }

            visitedFunctions.Add(function);

            // 计算当前函数的堆栈帧大小
            int currentFrameSize = _stackFrames[function].FrameSize;
            int maxChildStackUsage = 0;

            // 计算所有被调用函数的最大堆栈使用
            foreach (var call in function.CalledFunctions)
            {
                int childStackUsage = CalculateMaxStackUsage(call.Function, visitedFunctions);
                maxChildStackUsage = Math.Max(maxChildStackUsage, childStackUsage);
            }

            visitedFunctions.Remove(function);

            // 总的堆栈使用 = 当前函数堆栈帧 + 最大子函数堆栈使用
            int totalStackUsage = currentFrameSize + maxChildStackUsage;
            _maxStackUsage[function] = totalStackUsage;

            return totalStackUsage;
        }

        /// <summary>
        /// 分析任务堆栈使用
        /// </summary>
        private void AnalyzeTaskStackUsage(CallGraph callGraph, StackCalculationResult result)
        {
            var taskFunctions = callGraph.Functions.Values
                .Where(f => f.Type == FunctionType.TaskFunction)
                .ToList();

            foreach (var taskFunction in taskFunctions)
            {
                var taskAnalysis = new TaskStackAnalysis
                {
                    TaskName = taskFunction.Name,
                    TaskId = taskFunction.AutosarTaskId,
                    EntryFunction = taskFunction,
                    MaxStackUsage = _maxStackUsage.ContainsKey(taskFunction) ? _maxStackUsage[taskFunction] : 0
                };

                // 获取所有调用路径
                taskAnalysis.AllCallPaths = GetAllCallPaths(taskFunction);
                
                // 找到最深的调用路径
                taskAnalysis.DeepestCallPath = taskAnalysis.AllCallPaths
                    .OrderByDescending(p => p.TotalStackSize)
                    .FirstOrDefault();

                result.TaskAnalyses.Add(taskAnalysis);
            }
        }

        /// <summary>
        /// 分析中断堆栈使用
        /// </summary>
        private void AnalyzeInterruptStackUsage(CallGraph callGraph, StackCalculationResult result)
        {
            var interruptFunctions = callGraph.Functions.Values
                .Where(f => f.Type == FunctionType.InterruptHandler)
                .ToList();

            foreach (var interruptFunction in interruptFunctions)
            {
                var interruptAnalysis = new InterruptStackAnalysis
                {
                    InterruptName = interruptFunction.Name,
                    Priority = interruptFunction.InterruptPriority ?? 0,
                    HandlerFunction = interruptFunction,
                    MaxStackUsage = _maxStackUsage.ContainsKey(interruptFunction) ? _maxStackUsage[interruptFunction] : 0,
                    ContextSaveSize = CalculateInterruptContextSize()
                };

                // 获取最深调用路径
                var callPaths = GetAllCallPaths(interruptFunction);
                interruptAnalysis.DeepestCallPath = callPaths
                    .OrderByDescending(p => p.TotalStackSize)
                    .FirstOrDefault();

                result.InterruptAnalyses.Add(interruptAnalysis);
            }
        }

        /// <summary>
        /// 获取函数的所有调用路径
        /// </summary>
        private List<CallPath> GetAllCallPaths(Function startFunction)
        {
            var paths = new List<CallPath>();
            var currentPath = new CallPath();
            var visited = new HashSet<Function>();

            GetCallPathsRecursive(startFunction, currentPath, paths, visited, 50); // 最大深度50

            // 计算每个路径的堆栈大小
            foreach (var path in paths)
            {
                path.CalculateTotalStackSize();
            }

            return paths;
        }

        /// <summary>
        /// 递归获取调用路径
        /// </summary>
        private void GetCallPathsRecursive(Function current, CallPath currentPath, 
            List<CallPath> allPaths, HashSet<Function> visited, int remainingDepth)
        {
            if (remainingDepth <= 0 || visited.Contains(current))
                return;

            visited.Add(current);
            currentPath.Functions.Add(current);

            if (current.CalledFunctions.Count == 0)
            {
                // 叶子节点，添加路径
                allPaths.Add(new CallPath(currentPath));
            }
            else
            {
                foreach (var call in current.CalledFunctions)
                {
                    GetCallPathsRecursive(call.Function, currentPath, allPaths, visited, remainingDepth - 1);
                }
            }

            currentPath.Functions.RemoveAt(currentPath.Functions.Count - 1);
            visited.Remove(current);
        }

        /// <summary>
        /// 计算中断上下文保存大小
        /// </summary>
        private int CalculateInterruptContextSize()
        {
            switch (_architecture)
            {
                case ProcessorArchitecture.RH850:
                    // RH850 V850架构的中断上下文
                    return 32 * 4; // 32个寄存器 * 4字节

                case ProcessorArchitecture.TC377:
                    // TC377 TriCore架构的中断上下文
                    return 16 * 4; // 16个寄存器 * 4字节

                default:
                    return 64; // 默认值
            }
        }

        /// <summary>
        /// 检测堆栈使用问题
        /// </summary>
        private void DetectStackIssues(StackCalculationResult result)
        {
            // 检测任务堆栈问题
            foreach (var taskAnalysis in result.TaskAnalyses)
            {
                if (taskAnalysis.HasOverflowRisk)
                {
                    result.Warnings.Add(new StackWarning
                    {
                        Type = WarningType.StackOverflow,
                        Level = WarningLevel.Critical,
                        Message = $"任务 '{taskAnalysis.TaskName}' 存在堆栈溢出风险",
                        RelatedFunction = taskAnalysis.EntryFunction,
                        RelatedContext = taskAnalysis.TaskName,
                        Suggestion = $"建议将堆栈大小从 {taskAnalysis.ConfiguredStackSize} 增加到至少 {taskAnalysis.MaxStackUsage + 512}"
                    });
                }
                else if (taskAnalysis.StackUtilization > 0.8)
                {
                    result.Warnings.Add(new StackWarning
                    {
                        Type = WarningType.InsufficientStack,
                        Level = WarningLevel.Warning,
                        Message = $"任务 '{taskAnalysis.TaskName}' 堆栈使用率过高 ({taskAnalysis.StackUtilization:P1})",
                        RelatedFunction = taskAnalysis.EntryFunction,
                        RelatedContext = taskAnalysis.TaskName,
                        Suggestion = "建议增加堆栈大小以提供更多安全余量"
                    });
                }
            }

            // 检测递归调用问题
            foreach (var function in _stackFrames.Keys.Where(f => f.IsRecursive))
            {
                result.Warnings.Add(new StackWarning
                {
                    Type = WarningType.RecursiveCall,
                    Level = WarningLevel.Warning,
                    Message = $"函数 '{function.Name}' 包含递归调用",
                    RelatedFunction = function,
                    Suggestion = "递归调用可能导致堆栈溢出，建议限制递归深度或改为迭代实现"
                });
            }

            // 检测大型局部变量
            foreach (var kvp in _stackFrames)
            {
                var function = kvp.Key;
                var frame = kvp.Value;

                if (frame.LocalVariablesSize > 1024) // 大于1KB的局部变量
                {
                    result.Warnings.Add(new StackWarning
                    {
                        Type = WarningType.LargeLocalVariable,
                        Level = WarningLevel.Warning,
                        Message = $"函数 '{function.Name}' 包含大型局部变量 ({frame.LocalVariablesSize} 字节)",
                        RelatedFunction = function,
                        Suggestion = "考虑将大型局部变量改为动态分配或静态变量"
                    });
                }
            }

            // 检测深度调用链
            var maxCallDepth = result.EntryPointStackUsages.Values.Max();
            if (maxCallDepth > 10000) // 假设超过10KB为深度调用链
            {
                result.Warnings.Add(new StackWarning
                {
                    Type = WarningType.DeepCallChain,
                    Level = WarningLevel.Warning,
                    Message = $"检测到深度调用链，最大堆栈使用 {maxCallDepth} 字节",
                    Suggestion = "考虑重构代码以减少调用深度"
                });
            }
        }

        /// <summary>
        /// 获取函数的堆栈帧信息
        /// </summary>
        public StackFrame GetStackFrame(Function function)
        {
            return _stackFrames.ContainsKey(function) ? _stackFrames[function] : null;
        }

        /// <summary>
        /// 获取函数的最大堆栈使用量
        /// </summary>
        public int GetMaxStackUsage(Function function)
        {
            return _maxStackUsage.ContainsKey(function) ? _maxStackUsage[function] : 0;
        }
    }

    /// <summary>
    /// 堆栈计算结果
    /// </summary>
    public class StackCalculationResult
    {
        public ProcessorArchitecture Architecture { get; set; }
        public DateTime CalculationTime { get; set; }
        public bool IsSuccessful { get; set; }
        public string ErrorMessage { get; set; }
        
        public Dictionary<Function, int> EntryPointStackUsages { get; set; }
        public List<TaskStackAnalysis> TaskAnalyses { get; set; }
        public List<InterruptStackAnalysis> InterruptAnalyses { get; set; }
        public List<StackWarning> Warnings { get; set; }

        public StackCalculationResult()
        {
            EntryPointStackUsages = new Dictionary<Function, int>();
            TaskAnalyses = new List<TaskStackAnalysis>();
            InterruptAnalyses = new List<InterruptStackAnalysis>();
            Warnings = new List<StackWarning>();
        }

        /// <summary>
        /// 获取最大堆栈使用量
        /// </summary>
        public int GetMaxStackUsage()
        {
            int maxTaskStack = TaskAnalyses.Any() ? TaskAnalyses.Max(t => t.MaxStackUsage) : 0;
            int maxInterruptStack = InterruptAnalyses.Any() ? InterruptAnalyses.Max(i => i.MaxStackUsage) : 0;
            return Math.Max(maxTaskStack, maxInterruptStack);
        }

        /// <summary>
        /// 获取警告统计
        /// </summary>
        public Dictionary<WarningLevel, int> GetWarningStatistics()
        {
            return Warnings.GroupBy(w => w.Level)
                          .ToDictionary(g => g.Key, g => g.Count());
        }
    }
}
